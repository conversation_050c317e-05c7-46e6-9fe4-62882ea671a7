const { validationResult } = require('express-validator');
const LiveCodeService = require('../services/liveCodeService');

class PollingStringController {
    constructor() {
        this.liveCodeService = new LiveCodeService();
    }

    // 基础CRUD操作
    async getPollingStrings(req, res) {
        try {
            const liveCodeId = parseInt(req.params.liveCodeId);
            const userId = req.user.id;
            const userRole = req.user.role;
            const { page = 1, limit = 20, search, is_active } = req.query;

            // 检查活码权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            const options = {
                page: parseInt(page),
                limit: parseInt(limit)
            };

            if (search) options.search = search;
            if (is_active !== undefined) options.is_active = parseInt(is_active);

            const result = await this.liveCodeService.getPollingStrings(liveCodeId, options);

            res.json({
                success: true,
                data: result
            });
        } catch (error) {
            console.error('获取轮询字符串失败:', error);
            res.status(500).json({
                error: '获取轮询字符串失败',
                message: error.message
            });
        }
    }

    async addPollingString(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeId = parseInt(req.params.liveCodeId);
            const userId = req.user.id;
            const userRole = req.user.role;

            // 检查活码权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            const pollingString = await this.liveCodeService.addPollingString(liveCodeId, req.body);

            res.status(201).json({
                success: true,
                message: '轮询字符串添加成功',
                data: pollingString
            });
        } catch (error) {
            console.error('添加轮询字符串失败:', error);
            res.status(400).json({
                error: '添加轮询字符串失败',
                message: error.message
            });
        }
    }

    async updatePollingString(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeId = parseInt(req.params.liveCodeId);
            const pollingId = parseInt(req.params.pollingId);
            const userId = req.user.id;
            const userRole = req.user.role;

            // 检查活码权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            const pollingString = await this.liveCodeService.updatePollingString(
                liveCodeId, 
                pollingId, 
                req.body
            );

            res.json({
                success: true,
                message: '轮询字符串更新成功',
                data: pollingString
            });
        } catch (error) {
            console.error('更新轮询字符串失败:', error);
            res.status(400).json({
                error: '更新轮询字符串失败',
                message: error.message
            });
        }
    }

    async deletePollingString(req, res) {
        try {
            const liveCodeId = parseInt(req.params.liveCodeId);
            const pollingId = parseInt(req.params.pollingId);
            const userId = req.user.id;
            const userRole = req.user.role;

            // 检查活码权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            await this.liveCodeService.deletePollingString(liveCodeId, pollingId);

            res.json({
                success: true,
                message: '轮询字符串删除成功'
            });
        } catch (error) {
            console.error('删除轮询字符串失败:', error);
            res.status(400).json({
                error: '删除轮询字符串失败',
                message: error.message
            });
        }
    }

    // 批量操作
    async batchAddPollingStrings(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeId = parseInt(req.params.liveCodeId);
            const userId = req.user.id;
            const userRole = req.user.role;
            const { polling_strings } = req.body;

            // 检查活码权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            const results = [];
            for (const pollingData of polling_strings) {
                try {
                    const pollingString = await this.liveCodeService.addPollingString(liveCodeId, pollingData);
                    results.push({ success: true, data: pollingString });
                } catch (error) {
                    results.push({ 
                        success: false, 
                        error: error.message,
                        data: pollingData 
                    });
                }
            }

            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            res.status(201).json({
                success: true,
                message: `批量添加完成: ${successCount} 成功, ${failCount} 失败`,
                data: {
                    results,
                    summary: {
                        total: results.length,
                        success: successCount,
                        failed: failCount
                    }
                }
            });
        } catch (error) {
            console.error('批量添加轮询字符串失败:', error);
            res.status(500).json({
                error: '批量添加轮询字符串失败',
                message: error.message
            });
        }
    }

    async batchUpdatePollingStrings(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeId = parseInt(req.params.liveCodeId);
            const userId = req.user.id;
            const userRole = req.user.role;
            const { updates } = req.body;

            // 检查活码权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            const results = [];
            for (const update of updates) {
                try {
                    const pollingString = await this.liveCodeService.updatePollingString(
                        liveCodeId, 
                        update.id, 
                        update
                    );
                    results.push({ success: true, data: pollingString });
                } catch (error) {
                    results.push({ 
                        success: false, 
                        error: error.message,
                        id: update.id 
                    });
                }
            }

            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            res.json({
                success: true,
                message: `批量更新完成: ${successCount} 成功, ${failCount} 失败`,
                data: {
                    results,
                    summary: {
                        total: results.length,
                        success: successCount,
                        failed: failCount
                    }
                }
            });
        } catch (error) {
            console.error('批量更新轮询字符串失败:', error);
            res.status(500).json({
                error: '批量更新轮询字符串失败',
                message: error.message
            });
        }
    }

    async batchDeletePollingStrings(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeId = parseInt(req.params.liveCodeId);
            const userId = req.user.id;
            const userRole = req.user.role;
            const { ids } = req.body;

            // 检查活码权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            const results = [];
            for (const id of ids) {
                try {
                    await this.liveCodeService.deletePollingString(liveCodeId, id);
                    results.push({ success: true, id });
                } catch (error) {
                    results.push({ 
                        success: false, 
                        error: error.message,
                        id 
                    });
                }
            }

            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            res.json({
                success: true,
                message: `批量删除完成: ${successCount} 成功, ${failCount} 失败`,
                data: {
                    results,
                    summary: {
                        total: results.length,
                        success: successCount,
                        failed: failCount
                    }
                }
            });
        } catch (error) {
            console.error('批量删除轮询字符串失败:', error);
            res.status(500).json({
                error: '批量删除轮询字符串失败',
                message: error.message
            });
        }
    }

    async batchToggleStatus(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeId = parseInt(req.params.liveCodeId);
            const userId = req.user.id;
            const userRole = req.user.role;
            const { ids, is_active } = req.body;

            // 检查活码权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            const results = [];
            for (const id of ids) {
                try {
                    const pollingString = await this.liveCodeService.updatePollingString(
                        liveCodeId,
                        id,
                        { is_active }
                    );
                    results.push({ success: true, data: pollingString });
                } catch (error) {
                    results.push({
                        success: false,
                        error: error.message,
                        id
                    });
                }
            }

            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            res.json({
                success: true,
                message: `批量${is_active ? '启用' : '禁用'}完成: ${successCount} 成功, ${failCount} 失败`,
                data: {
                    results,
                    summary: {
                        total: results.length,
                        success: successCount,
                        failed: failCount
                    }
                }
            });
        } catch (error) {
            console.error('批量切换状态失败:', error);
            res.status(500).json({
                error: '批量切换状态失败',
                message: error.message
            });
        }
    }

    // 排序和权重管理
    async reorderPollingStrings(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeId = parseInt(req.params.liveCodeId);
            const userId = req.user.id;
            const userRole = req.user.role;
            const { order } = req.body;

            // 检查活码权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            // 重新排序轮询字符串
            await this.liveCodeService.reorderPollingStrings(liveCodeId, order);

            res.json({
                success: true,
                message: '轮询字符串排序成功'
            });
        } catch (error) {
            console.error('重新排序轮询字符串失败:', error);
            res.status(500).json({
                error: '重新排序轮询字符串失败',
                message: error.message
            });
        }
    }

    async autoAssignWeights(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeId = parseInt(req.params.liveCodeId);
            const userId = req.user.id;
            const userRole = req.user.role;
            const { strategy } = req.body;

            // 检查活码权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            // 自动分配权重
            const result = await this.liveCodeService.autoAssignWeights(liveCodeId, strategy);

            res.json({
                success: true,
                message: `权重自动分配完成 (${strategy} 策略)`,
                data: result
            });
        } catch (error) {
            console.error('自动分配权重失败:', error);
            res.status(500).json({
                error: '自动分配权重失败',
                message: error.message
            });
        }
    }

    // 导入导出
    async importPollingStrings(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeId = parseInt(req.params.liveCodeId);
            const userId = req.user.id;
            const userRole = req.user.role;
            const { format, data, options = {} } = req.body;

            // 检查活码权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            const result = await this.liveCodeService.importPollingStrings(
                liveCodeId,
                format,
                data,
                options
            );

            res.json({
                success: true,
                message: `导入完成: ${result.success} 成功, ${result.failed} 失败`,
                data: result
            });
        } catch (error) {
            console.error('导入轮询字符串失败:', error);
            res.status(400).json({
                error: '导入轮询字符串失败',
                message: error.message
            });
        }
    }

    async exportPollingStrings(req, res) {
        try {
            const liveCodeId = parseInt(req.params.liveCodeId);
            const userId = req.user.id;
            const userRole = req.user.role;
            const { format = 'json' } = req.query;

            // 检查活码权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            const exportData = await this.liveCodeService.exportPollingStrings(liveCodeId, format);

            // 设置响应头
            const filename = `polling_strings_${liveCodeId}_${Date.now()}.${format}`;
            res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

            if (format === 'json') {
                res.setHeader('Content-Type', 'application/json');
                res.json(exportData);
            } else if (format === 'csv') {
                res.setHeader('Content-Type', 'text/csv');
                res.send(exportData);
            } else {
                res.setHeader('Content-Type', 'text/plain');
                res.send(exportData);
            }
        } catch (error) {
            console.error('导出轮询字符串失败:', error);
            res.status(500).json({
                error: '导出轮询字符串失败',
                message: error.message
            });
        }
    }

    async getImportTemplate(req, res) {
        try {
            const { format = 'csv' } = req.query;

            const template = this.liveCodeService.getPollingStringImportTemplate(format);

            const filename = `polling_strings_template.${format}`;
            res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

            if (format === 'json') {
                res.setHeader('Content-Type', 'application/json');
                res.json(template);
            } else {
                res.setHeader('Content-Type', 'text/csv');
                res.send(template);
            }
        } catch (error) {
            console.error('获取导入模板失败:', error);
            res.status(500).json({
                error: '获取导入模板失败',
                message: error.message
            });
        }
    }
}

module.exports = PollingStringController;
