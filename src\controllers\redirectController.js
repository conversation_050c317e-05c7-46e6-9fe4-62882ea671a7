const RedirectService = require('../services/redirectService');

class RedirectController {
    constructor() {
        this.redirectService = new RedirectService();
    }

    // 处理活码重定向
    async handleRedirect(req, res) {
        try {
            const code = req.params.code;

            // 验证活码格式
            if (!this.redirectService.isValidLiveCode(code)) {
                return res.status(404).render('error', {
                    title: '活码无效',
                    message: '您访问的活码格式不正确',
                    code: 404
                });
            }

            // 处理重定向
            const result = await this.redirectService.handleRedirect(code, req);

            // 执行302重定向
            res.redirect(302, result.redirectUrl);

        } catch (error) {
            console.error('重定向失败:', error);
            
            // 根据错误类型返回不同的错误页面
            let statusCode = 500;
            let message = '服务暂时不可用，请稍后重试';

            if (error.message === '活码不存在') {
                statusCode = 404;
                message = '您访问的活码不存在';
            } else if (error.message === '活码已禁用') {
                statusCode = 403;
                message = '该活码已被禁用';
            } else if (error.message === '没有可用的轮询字符串') {
                statusCode = 503;
                message = '服务配置错误，请联系管理员';
            }

            // 如果是API请求，返回JSON
            if (req.headers.accept && req.headers.accept.includes('application/json')) {
                return res.status(statusCode).json({
                    error: '重定向失败',
                    message: error.message
                });
            }

            // 否则渲染错误页面
            res.status(statusCode).render('error', {
                title: '访问失败',
                message: message,
                code: statusCode
            });
        }
    }

    // 检查活码健康状态
    async checkHealth(req, res) {
        try {
            const code = req.params.code;

            if (!this.redirectService.isValidLiveCode(code)) {
                return res.status(400).json({
                    error: '活码格式无效',
                    message: '活码格式不正确'
                });
            }

            const health = await this.redirectService.checkLiveCodeHealth(code);

            res.json({
                success: true,
                data: health
            });
        } catch (error) {
            console.error('检查活码健康状态失败:', error);
            res.status(500).json({
                error: '检查失败',
                message: error.message
            });
        }
    }

    // 批量检查活码健康状态
    async batchCheckHealth(req, res) {
        try {
            const { codes } = req.body;

            if (!Array.isArray(codes) || codes.length === 0) {
                return res.status(400).json({
                    error: '输入验证失败',
                    message: 'codes必须是非空数组'
                });
            }

            // 验证所有活码格式
            const invalidCodes = codes.filter(code => !this.redirectService.isValidLiveCode(code));
            if (invalidCodes.length > 0) {
                return res.status(400).json({
                    error: '活码格式无效',
                    message: `以下活码格式不正确: ${invalidCodes.join(', ')}`
                });
            }

            const results = await this.redirectService.batchCheckHealth(codes);

            res.json({
                success: true,
                data: results
            });
        } catch (error) {
            console.error('批量检查活码健康状态失败:', error);
            res.status(500).json({
                error: '批量检查失败',
                message: error.message
            });
        }
    }

    // 预热活码
    async warmupLiveCode(req, res) {
        try {
            const code = req.params.code;

            if (!this.redirectService.isValidLiveCode(code)) {
                return res.status(400).json({
                    error: '活码格式无效',
                    message: '活码格式不正确'
                });
            }

            const success = await this.redirectService.warmupLiveCode(code);

            res.json({
                success: success,
                message: success ? '活码预热成功' : '活码预热失败'
            });
        } catch (error) {
            console.error('预热活码失败:', error);
            res.status(500).json({
                error: '预热失败',
                message: error.message
            });
        }
    }

    // 获取重定向统计信息
    async getRedirectStats(req, res) {
        try {
            const { 
                start_date, 
                end_date, 
                live_code_id 
            } = req.query;

            const options = {};
            if (start_date) options.start_date = start_date;
            if (end_date) options.end_date = end_date;
            if (live_code_id) options.live_code_id = parseInt(live_code_id);

            const stats = await this.redirectService.getRedirectStats(options);

            res.json({
                success: true,
                data: stats
            });
        } catch (error) {
            console.error('获取重定向统计失败:', error);
            res.status(500).json({
                error: '获取统计失败',
                message: error.message
            });
        }
    }

    // 获取热门重定向
    async getTopRedirects(req, res) {
        try {
            const { 
                limit = 10, 
                start_date, 
                end_date, 
                live_code_id 
            } = req.query;

            const options = {};
            if (start_date) options.start_date = start_date;
            if (end_date) options.end_date = end_date;
            if (live_code_id) options.live_code_id = parseInt(live_code_id);

            const topRedirects = await this.redirectService.getTopRedirects(parseInt(limit), options);

            res.json({
                success: true,
                data: topRedirects
            });
        } catch (error) {
            console.error('获取热门重定向失败:', error);
            res.status(500).json({
                error: '获取热门重定向失败',
                message: error.message
            });
        }
    }

    // 获取地理位置统计
    async getGeoStats(req, res) {
        try {
            const { 
                limit = 50, 
                start_date, 
                end_date, 
                live_code_id 
            } = req.query;

            const options = {};
            if (start_date) options.start_date = start_date;
            if (end_date) options.end_date = end_date;
            if (live_code_id) options.live_code_id = parseInt(live_code_id);
            if (limit) options.limit = parseInt(limit);

            const geoStats = await this.redirectService.getGeoStats(options);

            res.json({
                success: true,
                data: geoStats
            });
        } catch (error) {
            console.error('获取地理位置统计失败:', error);
            res.status(500).json({
                error: '获取地理位置统计失败',
                message: error.message
            });
        }
    }

    // 获取时间统计
    async getTimeStats(req, res) {
        try {
            const { 
                period = 'day', 
                limit = 30, 
                start_date, 
                end_date, 
                live_code_id 
            } = req.query;

            // 验证period参数
            const validPeriods = ['hour', 'day', 'month'];
            if (!validPeriods.includes(period)) {
                return res.status(400).json({
                    error: '参数错误',
                    message: 'period参数必须是hour、day或month之一'
                });
            }

            const options = {};
            if (start_date) options.start_date = start_date;
            if (end_date) options.end_date = end_date;
            if (live_code_id) options.live_code_id = parseInt(live_code_id);
            if (limit) options.limit = parseInt(limit);

            const timeStats = await this.redirectService.getTimeStats(period, options);

            res.json({
                success: true,
                data: timeStats
            });
        } catch (error) {
            console.error('获取时间统计失败:', error);
            res.status(500).json({
                error: '获取时间统计失败',
                message: error.message
            });
        }
    }

    // 活码信息预览（不执行重定向）
    async previewLiveCode(req, res) {
        try {
            const code = req.params.code;

            if (!this.redirectService.isValidLiveCode(code)) {
                return res.status(400).json({
                    error: '活码格式无效',
                    message: '活码格式不正确'
                });
            }

            // 模拟重定向过程但不实际重定向
            const result = await this.redirectService.handleRedirect(code, req);

            res.json({
                success: true,
                data: {
                    live_code: result.liveCode.code,
                    title: result.liveCode.title,
                    description: result.liveCode.description,
                    redirect_url: result.redirectUrl,
                    polling_string: result.pollingString,
                    click_count: result.liveCode.click_count
                }
            });
        } catch (error) {
            console.error('预览活码失败:', error);
            res.status(400).json({
                error: '预览失败',
                message: error.message
            });
        }
    }
}

module.exports = RedirectController;
