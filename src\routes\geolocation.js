const express = require('express');
const { body } = require('express-validator');
const GeoLocationController = require('../controllers/geoLocationController');
const { 
    authenticateToken, 
    requireBoss, 
    checkResourceAccess 
} = require('../middleware/auth');

const router = express.Router();
const geoLocationController = new GeoLocationController();

// 所有路由都需要认证和Boss权限（地理位置管理是高级功能）
router.use(authenticateToken);
router.use(checkResourceAccess);
router.use(requireBoss);

// 服务管理路由
router.get('/status', (req, res) => {
    geoLocationController.getStatus(req, res);
});

router.get('/database-info', (req, res) => {
    geoLocationController.getDatabaseInfo(req, res);
});

router.post('/initialize', (req, res) => {
    geoLocationController.initializeService(req, res);
});

router.get('/test', (req, res) => {
    geoLocationController.testService(req, res);
});

// IP查询路由
router.get('/lookup/:ip', (req, res) => {
    geoLocationController.lookupIP(req, res);
});

router.post('/lookup/batch', 
    body('ips')
        .isArray({ min: 1, max: 100 })
        .withMessage('ips必须是包含1-100个元素的数组'),
    body('ips.*')
        .isString()
        .withMessage('每个IP地址必须是字符串'),
    (req, res) => {
        geoLocationController.batchLookupIPs(req, res);
    }
);

router.get('/current', (req, res) => {
    geoLocationController.getCurrentLocation(req, res);
});

router.get('/validate/:ip', (req, res) => {
    geoLocationController.validateIP(req, res);
});

// 统计路由
router.get('/stats', (req, res) => {
    geoLocationController.getGeoStats(req, res);
});

// 错误处理中间件
router.use((error, req, res, next) => {
    console.error('地理位置路由错误:', error);
    res.status(500).json({
        error: '服务器内部错误',
        message: process.env.NODE_ENV === 'development' ? error.message : '请稍后重试'
    });
});

module.exports = router;
