const { validationResult } = require('express-validator');
const AccessLog = require('../models/AccessLog');
const LiveCodeService = require('../services/liveCodeService');

class AnalyticsController {
    constructor() {
        this.accessLogModel = new AccessLog();
        this.liveCodeService = new LiveCodeService();
    }

    // 概览统计
    async getOverview(req, res) {
        try {
            const userId = req.user.id;
            const userRole = req.user.role;
            const { period = 'week' } = req.query;

            // 根据用户角色获取不同的统计数据
            let overview = {};

            if (userRole === 'manager') {
                // Manager只能看自己的数据
                overview = await this.getManagerOverview(userId, period);
            } else {
                // Boss和Super Admin可以看全局数据
                overview = await this.getGlobalOverview(period);
            }

            res.json({
                success: true,
                data: overview
            });
        } catch (error) {
            console.error('获取概览统计失败:', error);
            res.status(500).json({
                error: '获取概览统计失败',
                message: error.message
            });
        }
    }

    async getDashboardData(req, res) {
        try {
            const userId = req.user.id;
            const userRole = req.user.role;

            // 获取仪表板数据
            const dashboardData = {
                overview: await this.getOverviewStats(userId, userRole),
                recent_activity: await this.getRecentActivity(userId, userRole),
                top_livecodes: await this.getTopLiveCodes(userId, userRole, 5),
                geo_stats: await this.getTopCountries(userId, userRole, 5),
                time_stats: await this.getTimeDistribution(userId, userRole)
            };

            res.json({
                success: true,
                data: dashboardData
            });
        } catch (error) {
            console.error('获取仪表板数据失败:', error);
            res.status(500).json({
                error: '获取仪表板数据失败',
                message: error.message
            });
        }
    }

    // 活码统计
    async getLiveCodeStats(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const userId = req.user.id;
            const userRole = req.user.role;
            const { period = 'week', start_date, end_date } = req.query;

            const stats = await this.liveCodeService.getLiveCodeStats(
                userId, 
                userRole, 
                { period, start_date, end_date }
            );

            res.json({
                success: true,
                data: stats
            });
        } catch (error) {
            console.error('获取活码统计失败:', error);
            res.status(500).json({
                error: '获取活码统计失败',
                message: error.message
            });
        }
    }

    async getLiveCodeDetailStats(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeId = parseInt(req.params.id);
            const userId = req.user.id;
            const userRole = req.user.role;
            const { period = 'week', start_date, end_date } = req.query;

            // 检查权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            const stats = await this.getLiveCodeDetailedStats(
                liveCodeId, 
                { period, start_date, end_date }
            );

            res.json({
                success: true,
                data: stats
            });
        } catch (error) {
            console.error('获取活码详细统计失败:', error);
            res.status(500).json({
                error: '获取活码详细统计失败',
                message: error.message
            });
        }
    }

    async getTopLiveCodes(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const userId = req.user.id;
            const userRole = req.user.role;
            const { 
                limit = 10, 
                period = 'week', 
                metric = 'clicks' 
            } = req.query;

            const topLiveCodes = await this.liveCodeService.getTopLiveCodes(
                parseInt(limit),
                userId,
                userRole,
                { period, metric }
            );

            res.json({
                success: true,
                data: topLiveCodes
            });
        } catch (error) {
            console.error('获取热门活码失败:', error);
            res.status(500).json({
                error: '获取热门活码失败',
                message: error.message
            });
        }
    }

    // 访问统计
    async getVisitTimeline(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const userId = req.user.id;
            const userRole = req.user.role;
            const { 
                granularity = 'day', 
                start_date, 
                end_date, 
                live_code_id 
            } = req.query;

            const timeline = await this.accessLogModel.getTimeline({
                granularity,
                start_date,
                end_date,
                live_code_id: live_code_id ? parseInt(live_code_id) : null,
                user_id: userRole === 'manager' ? userId : null
            });

            res.json({
                success: true,
                data: timeline
            });
        } catch (error) {
            console.error('获取访问时间线失败:', error);
            res.status(500).json({
                error: '获取访问时间线失败',
                message: error.message
            });
        }
    }

    async getRealtimeVisits(req, res) {
        try {
            const userId = req.user.id;
            const userRole = req.user.role;

            // 获取最近5分钟的访问数据
            const realtimeData = await this.accessLogModel.getRealtimeData({
                minutes: 5,
                user_id: userRole === 'manager' ? userId : null
            });

            res.json({
                success: true,
                data: realtimeData
            });
        } catch (error) {
            console.error('获取实时访问数据失败:', error);
            res.status(500).json({
                error: '获取实时访问数据失败',
                message: error.message
            });
        }
    }

    async getVisitSummary(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const userId = req.user.id;
            const userRole = req.user.role;
            const { period = 'today' } = req.query;

            const summary = await this.accessLogModel.getSummary({
                period,
                user_id: userRole === 'manager' ? userId : null
            });

            res.json({
                success: true,
                data: summary
            });
        } catch (error) {
            console.error('获取访问摘要失败:', error);
            res.status(500).json({
                error: '获取访问摘要失败',
                message: error.message
            });
        }
    }

    // 地理位置统计
    async getCountryStats(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const userId = req.user.id;
            const userRole = req.user.role;
            const { 
                limit = 20, 
                start_date, 
                end_date, 
                live_code_id 
            } = req.query;

            const countryStats = await this.accessLogModel.getCountryStats({
                limit: parseInt(limit),
                start_date,
                end_date,
                live_code_id: live_code_id ? parseInt(live_code_id) : null,
                user_id: userRole === 'manager' ? userId : null
            });

            res.json({
                success: true,
                data: countryStats
            });
        } catch (error) {
            console.error('获取国家统计失败:', error);
            res.status(500).json({
                error: '获取国家统计失败',
                message: error.message
            });
        }
    }

    async getCityStats(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const userId = req.user.id;
            const userRole = req.user.role;
            const { 
                limit = 20, 
                start_date, 
                end_date, 
                live_code_id 
            } = req.query;

            const cityStats = await this.accessLogModel.getCityStats({
                limit: parseInt(limit),
                start_date,
                end_date,
                live_code_id: live_code_id ? parseInt(live_code_id) : null,
                user_id: userRole === 'manager' ? userId : null
            });

            res.json({
                success: true,
                data: cityStats
            });
        } catch (error) {
            console.error('获取城市统计失败:', error);
            res.status(500).json({
                error: '获取城市统计失败',
                message: error.message
            });
        }
    }

    async getMapData(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const userId = req.user.id;
            const userRole = req.user.role;
            const { start_date, end_date, live_code_id } = req.query;

            const mapData = await this.accessLogModel.getMapData({
                start_date,
                end_date,
                live_code_id: live_code_id ? parseInt(live_code_id) : null,
                user_id: userRole === 'manager' ? userId : null
            });

            res.json({
                success: true,
                data: mapData
            });
        } catch (error) {
            console.error('获取地图数据失败:', error);
            res.status(500).json({
                error: '获取地图数据失败',
                message: error.message
            });
        }
    }

    // 辅助方法
    async getManagerOverview(userId, period) {
        // Manager专用概览统计
        return {
            total_livecodes: 0,
            active_livecodes: 0,
            total_clicks: 0,
            unique_visitors: 0,
            period: period
        };
    }

    async getGlobalOverview(period) {
        // 全局概览统计
        return {
            total_livecodes: 0,
            active_livecodes: 0,
            total_users: 0,
            total_clicks: 0,
            unique_visitors: 0,
            period: period
        };
    }

    async getOverviewStats(userId, userRole) {
        // 获取概览统计数据
        return {};
    }

    async getRecentActivity(userId, userRole) {
        // 获取最近活动
        return [];
    }

    async getTopCountries(userId, userRole, limit) {
        // 获取热门国家
        return [];
    }

    async getTimeDistribution(userId, userRole) {
        // 获取时间分布
        return {};
    }

    async getLiveCodeDetailedStats(liveCodeId, options) {
        // 获取活码详细统计
        return {};
    }
}

module.exports = AnalyticsController;
