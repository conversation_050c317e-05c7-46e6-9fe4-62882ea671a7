const express = require('express');
const { body, validationResult } = require('express-validator');

// 导入服务和模型
const AuthService = require('../services/authService');
const LiveCodeService = require('../services/liveCodeService');
const AccessLog = require('../models/AccessLog');
const User = require('../models/User');

// 导入中间件
const { 
    authenticateToken, 
    requireSuperAdmin, 
    requireBoss, 
    checkResourceAccess 
} = require('../middleware/auth');

const router = express.Router();

// 所有管理员路由都需要认证
router.use(authenticateToken);
router.use(checkResourceAccess);

// 实例化服务
const authService = new AuthService();
const liveCodeService = new LiveCodeService();
const accessLogModel = new AccessLog();
const userModel = new User();

// 管理员仪表板概览
router.get('/dashboard', requireBoss, async (req, res) => {
    try {
        const userRole = req.user.role;
        const userId = req.user.id;

        // 获取基础统计信息
        let stats = {};

        if (userRole === 'manager') {
            // Manager只能看自己的数据
            stats = await liveCodeService.getLiveCodeStats(userId, userRole);
        } else {
            // Boss和Super Admin可以看全局数据
            stats = await liveCodeService.getLiveCodeStats();
        }

        // 获取用户统计（仅Boss和Super Admin）
        let userStats = null;
        if (['super_admin', 'boss'].includes(userRole)) {
            userStats = userModel.getStats();
        }

        // 获取今日访问统计
        const todayStats = accessLogModel.getTodayStats();

        // 获取热门活码
        const topLiveCodes = await liveCodeService.getTopLiveCodes(5, userId, userRole);

        res.json({
            success: true,
            data: {
                livecode_stats: stats,
                user_stats: userStats,
                today_stats: todayStats,
                top_livecodes: topLiveCodes,
                user_info: {
                    username: req.user.username,
                    role: req.user.role,
                    permissions: {
                        can_manage_users: ['super_admin', 'boss'].includes(userRole),
                        can_view_global_stats: ['super_admin', 'boss'].includes(userRole),
                        can_export_data: ['super_admin', 'boss'].includes(userRole)
                    }
                }
            }
        });
    } catch (error) {
        console.error('获取管理员仪表板数据失败:', error);
        res.status(500).json({
            error: '获取仪表板数据失败',
            message: error.message
        });
    }
});

// 用户管理API（仅Boss和Super Admin）
router.get('/users', requireBoss, async (req, res) => {
    try {
        const { page = 1, limit = 20, search, role, is_active } = req.query;

        const options = {
            limit: parseInt(limit),
            offset: (parseInt(page) - 1) * parseInt(limit)
        };

        if (search) options.search = search;
        if (role) options.role = role;
        if (is_active !== undefined) options.is_active = parseInt(is_active);

        const users = userModel.findAll(options);
        const total = userModel.count(options);

        res.json({
            success: true,
            data: {
                users: users.map(user => ({
                    ...user,
                    password: undefined // 不返回密码
                })),
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total: total,
                    pages: Math.ceil(total / parseInt(limit))
                }
            }
        });
    } catch (error) {
        console.error('获取用户列表失败:', error);
        res.status(500).json({
            error: '获取用户列表失败',
            message: error.message
        });
    }
});

router.post('/users', 
    requireBoss,
    body('username').isLength({ min: 3, max: 50 }).matches(/^[a-zA-Z0-9_]+$/),
    body('email').isEmail().normalizeEmail(),
    body('password').isLength({ min: 6 }).matches(/^(?=.*[a-zA-Z])(?=.*\d)/),
    body('role').isIn(['super_admin', 'boss', 'manager']),
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const userData = req.body;
            const user = await authService.register(userData);

            res.status(201).json({
                success: true,
                message: '用户创建成功',
                data: {
                    ...user,
                    password: undefined
                }
            });
        } catch (error) {
            console.error('创建用户失败:', error);
            res.status(400).json({
                error: '创建用户失败',
                message: error.message
            });
        }
    }
);

router.put('/users/:id', 
    requireBoss,
    body('username').optional().isLength({ min: 3, max: 50 }).matches(/^[a-zA-Z0-9_]+$/),
    body('email').optional().isEmail().normalizeEmail(),
    body('role').optional().isIn(['super_admin', 'boss', 'manager']),
    body('is_active').optional().isBoolean(),
    async (req, res) => {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const userId = parseInt(req.params.id);
            const updateData = req.body;

            const user = await authService.updateUser(userId, updateData);

            res.json({
                success: true,
                message: '用户更新成功',
                data: {
                    ...user,
                    password: undefined
                }
            });
        } catch (error) {
            console.error('更新用户失败:', error);
            res.status(400).json({
                error: '更新用户失败',
                message: error.message
            });
        }
    }
);

router.delete('/users/:id', requireBoss, async (req, res) => {
    try {
        const userId = parseInt(req.params.id);

        // 防止删除自己
        if (userId === req.user.id) {
            return res.status(400).json({
                error: '操作失败',
                message: '不能删除自己的账户'
            });
        }

        const result = await authService.deleteUser(userId);

        res.json({
            success: true,
            message: result.message
        });
    } catch (error) {
        console.error('删除用户失败:', error);
        res.status(400).json({
            error: '删除用户失败',
            message: error.message
        });
    }
});

// 系统日志查看（仅Super Admin）
router.get('/logs/access', requireSuperAdmin, async (req, res) => {
    try {
        const { 
            page = 1, 
            limit = 50, 
            start_date, 
            end_date, 
            live_code_id,
            ip_address 
        } = req.query;

        const options = {
            limit: parseInt(limit),
            offset: (parseInt(page) - 1) * parseInt(limit)
        };

        if (start_date) options.start_date = start_date;
        if (end_date) options.end_date = end_date;
        if (live_code_id) options.live_code_id = parseInt(live_code_id);
        if (ip_address) options.ip_address = ip_address;

        const logs = accessLogModel.findAll(options);
        const total = accessLogModel.count(options);

        res.json({
            success: true,
            data: {
                logs,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total: total,
                    pages: Math.ceil(total / parseInt(limit))
                }
            }
        });
    } catch (error) {
        console.error('获取访问日志失败:', error);
        res.status(500).json({
            error: '获取访问日志失败',
            message: error.message
        });
    }
});

// 系统统计API
router.get('/stats/overview', requireBoss, async (req, res) => {
    try {
        const { period = 'week' } = req.query;
        
        // 获取系统概览统计
        const overview = {
            total_users: userModel.count(),
            active_users: userModel.count({ is_active: 1 }),
            total_livecodes: 0, // 将从LiveCode模型获取
            active_livecodes: 0,
            total_clicks: accessLogModel.getTotalClicks(),
            today_clicks: accessLogModel.getTodayClicks(),
            unique_visitors: accessLogModel.getUniqueVisitors(period)
        };

        // 获取时间趋势数据
        const trends = accessLogModel.getTimeStats(period === 'week' ? 'day' : 'hour');

        res.json({
            success: true,
            data: {
                overview,
                trends,
                period
            }
        });
    } catch (error) {
        console.error('获取系统统计失败:', error);
        res.status(500).json({
            error: '获取系统统计失败',
            message: error.message
        });
    }
});

// 数据备份API（仅Super Admin）
router.post('/backup/create', requireSuperAdmin, async (req, res) => {
    try {
        // 这里将在后续实现数据库备份功能
        const backupId = Date.now().toString();
        
        res.json({
            success: true,
            message: '数据备份功能将在后续版本中实现',
            data: {
                backup_id: backupId,
                created_at: new Date().toISOString()
            }
        });
    } catch (error) {
        console.error('创建数据备份失败:', error);
        res.status(500).json({
            error: '创建数据备份失败',
            message: error.message
        });
    }
});

// 系统维护API（仅Super Admin）
router.post('/maintenance/cleanup', requireSuperAdmin, async (req, res) => {
    try {
        const { days = 30 } = req.body;
        
        // 清理旧的访问日志
        const deletedLogs = accessLogModel.cleanup(parseInt(days));
        
        res.json({
            success: true,
            message: `清理了 ${deletedLogs} 条 ${days} 天前的访问日志`,
            data: {
                deleted_logs: deletedLogs,
                cleanup_date: new Date().toISOString()
            }
        });
    } catch (error) {
        console.error('系统清理失败:', error);
        res.status(500).json({
            error: '系统清理失败',
            message: error.message
        });
    }
});

// 错误处理中间件
router.use((error, req, res, next) => {
    console.error('管理员路由错误:', error);
    res.status(500).json({
        error: '服务器内部错误',
        message: process.env.NODE_ENV === 'development' ? error.message : '请稍后重试'
    });
});

module.exports = router;
