{"name": "tiny-lru", "description": "Tiny LRU cache for Client or Server", "version": "11.3.3", "homepage": "https://github.com/avoidwork/tiny-lru", "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git://github.com/avoidwork/tiny-lru.git"}, "bugs": {"url": "https://github.com/avoidwork/tiny-lru/issues"}, "files": ["dist/tiny-lru.cjs", "dist/tiny-lru.js", "types/*.d.ts"], "license": "BSD-3-<PERSON><PERSON>", "source": "src/lru.js", "main": "dist/tiny-lru.cjs", "exports": {"types": "./types/lru.d.ts", "import": "./dist/tiny-lru.js", "require": "./dist/tiny-lru.cjs"}, "type": "module", "types": "types/lru.d.ts", "engines": {"node": ">=12"}, "engineStrict": true, "scripts": {"build": "npm run lint && npm run rollup && npm run mocha", "benchmark:modern": "node benchmarks/modern-benchmark.js", "benchmark:perf": "node benchmarks/performance-observer-benchmark.js", "benchmark:all": "npm run benchmark:modern && npm run benchmark:perf", "changelog": "auto-changelog -p", "lint": "eslint --fix *.js src/*.js test/*.js benchmarks/*.js", "mocha": "nyc mocha test/*.js", "rollup": "rollup --config", "test": "npm run lint && npm run mocha", "types": "npx -p typescript tsc src/lru.js --declaration --allowJs --emitDeclarationOnly --outDir types", "prepare": "husky"}, "devDependencies": {"@rollup/plugin-terser": "^0.4.4", "auto-changelog": "^2.5.0", "eslint": "^9.29.0", "husky": "^9.1.7", "mocha": "^11.7.0", "nyc": "^17.1.0", "rollup": "^4.43.0", "tinybench": "^4.0.1", "typescript": "^5.8.3"}, "keywords": ["LRU", "cache", "tiny", "client", "server", "least", "recently", "used"]}