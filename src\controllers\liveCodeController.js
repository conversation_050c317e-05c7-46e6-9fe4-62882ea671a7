const LiveCodeService = require('../services/liveCodeService');
const { validationResult } = require('express-validator');

class LiveCodeController {
    constructor() {
        this.liveCodeService = new LiveCodeService();
    }

    // 创建活码
    async createLiveCode(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeData = req.body;
            const userId = req.user.id;

            const liveCode = await this.liveCodeService.createLiveCode(liveCodeData, userId);

            res.status(201).json({
                success: true,
                message: '活码创建成功',
                data: liveCode
            });
        } catch (error) {
            console.error('创建活码失败:', error);
            res.status(400).json({
                error: '创建活码失败',
                message: error.message
            });
        }
    }

    // 获取活码列表
    async getLiveCodeList(req, res) {
        try {
            const { 
                page = 1, 
                limit = 20, 
                search, 
                is_active, 
                created_by 
            } = req.query;

            const options = {
                limit: parseInt(limit),
                offset: (parseInt(page) - 1) * parseInt(limit)
            };

            if (search) options.search = search;
            if (is_active !== undefined) options.is_active = parseInt(is_active);
            if (created_by && req.user.role !== 'manager') {
                options.created_by = parseInt(created_by);
            }

            const liveCodes = await this.liveCodeService.getLiveCodeList(
                options, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                data: liveCodes,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit)
                }
            });
        } catch (error) {
            console.error('获取活码列表失败:', error);
            res.status(500).json({
                error: '获取活码列表失败',
                message: error.message
            });
        }
    }

    // 获取活码详情
    async getLiveCodeById(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (!id) {
                return res.status(400).json({
                    error: '无效的活码ID',
                    message: '活码ID必须是正整数'
                });
            }

            const liveCode = await this.liveCodeService.getLiveCodeById(id, req.user.id);

            // 检查权限
            if (req.user.role === 'manager' && liveCode.created_by !== req.user.id) {
                return res.status(403).json({
                    error: '权限不足',
                    message: '只能查看自己创建的活码'
                });
            }

            res.json({
                success: true,
                data: liveCode
            });
        } catch (error) {
            console.error('获取活码详情失败:', error);
            res.status(404).json({
                error: '获取活码详情失败',
                message: error.message
            });
        }
    }

    // 更新活码
    async updateLiveCode(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const id = parseInt(req.params.id);
            const updateData = req.body;

            const liveCode = await this.liveCodeService.updateLiveCode(
                id, 
                updateData, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: '活码更新成功',
                data: liveCode
            });
        } catch (error) {
            console.error('更新活码失败:', error);
            res.status(400).json({
                error: '更新活码失败',
                message: error.message
            });
        }
    }

    // 删除活码
    async deleteLiveCode(req, res) {
        try {
            const id = parseInt(req.params.id);

            const result = await this.liveCodeService.deleteLiveCode(
                id, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: result.message
            });
        } catch (error) {
            console.error('删除活码失败:', error);
            res.status(400).json({
                error: '删除活码失败',
                message: error.message
            });
        }
    }

    // 复制活码
    async duplicateLiveCode(req, res) {
        try {
            const id = parseInt(req.params.id);

            const liveCode = await this.liveCodeService.duplicateLiveCode(
                id, 
                req.user.id, 
                req.user.role
            );

            res.status(201).json({
                success: true,
                message: '活码复制成功',
                data: liveCode
            });
        } catch (error) {
            console.error('复制活码失败:', error);
            res.status(400).json({
                error: '复制活码失败',
                message: error.message
            });
        }
    }

    // 添加轮询字符串
    async addPollingStrings(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeId = parseInt(req.params.id);
            const { strings, batch_name } = req.body;

            const result = await this.liveCodeService.addPollingStrings(
                liveCodeId, 
                strings, 
                batch_name, 
                req.user.id, 
                req.user.role
            );

            res.status(201).json({
                success: true,
                message: result.message,
                data: result
            });
        } catch (error) {
            console.error('添加轮询字符串失败:', error);
            res.status(400).json({
                error: '添加轮询字符串失败',
                message: error.message
            });
        }
    }

    // 更新轮询字符串
    async updatePollingString(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const id = parseInt(req.params.pollingId);
            const updateData = req.body;

            const pollingString = await this.liveCodeService.updatePollingString(
                id, 
                updateData, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: '轮询字符串更新成功',
                data: pollingString
            });
        } catch (error) {
            console.error('更新轮询字符串失败:', error);
            res.status(400).json({
                error: '更新轮询字符串失败',
                message: error.message
            });
        }
    }

    // 删除轮询字符串
    async deletePollingString(req, res) {
        try {
            const id = parseInt(req.params.pollingId);

            const result = await this.liveCodeService.deletePollingString(
                id, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: result.message
            });
        } catch (error) {
            console.error('删除轮询字符串失败:', error);
            res.status(400).json({
                error: '删除轮询字符串失败',
                message: error.message
            });
        }
    }

    // 重新排序轮询字符串
    async reorderPollingStrings(req, res) {
        try {
            const liveCodeId = parseInt(req.params.id);
            const { ordered_ids } = req.body;

            if (!Array.isArray(ordered_ids)) {
                return res.status(400).json({
                    error: '输入验证失败',
                    message: 'ordered_ids必须是数组'
                });
            }

            const result = await this.liveCodeService.reorderPollingStrings(
                liveCodeId, 
                ordered_ids, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: result.message
            });
        } catch (error) {
            console.error('重新排序轮询字符串失败:', error);
            res.status(400).json({
                error: '重新排序轮询字符串失败',
                message: error.message
            });
        }
    }

    // 批量操作轮询字符串
    async batchOperatePollingStrings(req, res) {
        try {
            const liveCodeId = parseInt(req.params.id);
            const { operation, data } = req.body;

            const result = await this.liveCodeService.batchOperatePollingStrings(
                liveCodeId, 
                operation, 
                data, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: result.message
            });
        } catch (error) {
            console.error('批量操作轮询字符串失败:', error);
            res.status(400).json({
                error: '批量操作轮询字符串失败',
                message: error.message
            });
        }
    }

    // 导入轮询字符串
    async importPollingStrings(req, res) {
        try {
            const liveCodeId = parseInt(req.params.id);
            const { text, batch_name, separator } = req.body;

            if (!text) {
                return res.status(400).json({
                    error: '输入验证失败',
                    message: '导入文本不能为空'
                });
            }

            const result = await this.liveCodeService.importPollingStrings(
                liveCodeId, 
                text, 
                { batch_name, separator }, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: result.message,
                data: result
            });
        } catch (error) {
            console.error('导入轮询字符串失败:', error);
            res.status(400).json({
                error: '导入轮询字符串失败',
                message: error.message
            });
        }
    }

    // 导出轮询字符串
    async exportPollingStrings(req, res) {
        try {
            const liveCodeId = parseInt(req.params.id);
            const { batch_name, is_active } = req.query;

            const options = {};
            if (batch_name) options.batch_name = batch_name;
            if (is_active !== undefined) options.is_active = parseInt(is_active);

            const result = await this.liveCodeService.exportPollingStrings(
                liveCodeId, 
                options, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                data: result
            });
        } catch (error) {
            console.error('导出轮询字符串失败:', error);
            res.status(400).json({
                error: '导出轮询字符串失败',
                message: error.message
            });
        }
    }

    // 获取活码统计信息
    async getLiveCodeStats(req, res) {
        try {
            const stats = await this.liveCodeService.getLiveCodeStats(
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                data: stats
            });
        } catch (error) {
            console.error('获取统计信息失败:', error);
            res.status(500).json({
                error: '获取统计信息失败',
                message: error.message
            });
        }
    }

    // 获取热门活码
    async getTopLiveCodes(req, res) {
        try {
            const { limit = 10 } = req.query;

            const topCodes = await this.liveCodeService.getTopLiveCodes(
                parseInt(limit), 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                data: topCodes
            });
        } catch (error) {
            console.error('获取热门活码失败:', error);
            res.status(500).json({
                error: '获取热门活码失败',
                message: error.message
            });
        }
    }
}

module.exports = LiveCodeController;
