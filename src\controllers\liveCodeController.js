const LiveCodeService = require('../services/liveCodeService');
const { validationResult } = require('express-validator');

class LiveCodeController {
    constructor() {
        this.liveCodeService = new LiveCodeService();
    }

    // 创建活码
    async createLiveCode(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeData = req.body;
            const userId = req.user.id;

            const liveCode = await this.liveCodeService.createLiveCode(liveCodeData, userId);

            res.status(201).json({
                success: true,
                message: '活码创建成功',
                data: liveCode
            });
        } catch (error) {
            console.error('创建活码失败:', error);
            res.status(400).json({
                error: '创建活码失败',
                message: error.message
            });
        }
    }

    // 获取活码列表
    async getLiveCodeList(req, res) {
        try {
            const { 
                page = 1, 
                limit = 20, 
                search, 
                is_active, 
                created_by 
            } = req.query;

            const options = {
                limit: parseInt(limit),
                offset: (parseInt(page) - 1) * parseInt(limit)
            };

            if (search) options.search = search;
            if (is_active !== undefined) options.is_active = parseInt(is_active);
            if (created_by && req.user.role !== 'manager') {
                options.created_by = parseInt(created_by);
            }

            const liveCodes = await this.liveCodeService.getLiveCodeList(
                options, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                data: liveCodes,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit)
                }
            });
        } catch (error) {
            console.error('获取活码列表失败:', error);
            res.status(500).json({
                error: '获取活码列表失败',
                message: error.message
            });
        }
    }

    // 获取活码详情
    async getLiveCodeById(req, res) {
        try {
            const id = parseInt(req.params.id);
            if (!id) {
                return res.status(400).json({
                    error: '无效的活码ID',
                    message: '活码ID必须是正整数'
                });
            }

            const liveCode = await this.liveCodeService.getLiveCodeById(id, req.user.id);

            // 检查权限
            if (req.user.role === 'manager' && liveCode.created_by !== req.user.id) {
                return res.status(403).json({
                    error: '权限不足',
                    message: '只能查看自己创建的活码'
                });
            }

            res.json({
                success: true,
                data: liveCode
            });
        } catch (error) {
            console.error('获取活码详情失败:', error);
            res.status(404).json({
                error: '获取活码详情失败',
                message: error.message
            });
        }
    }

    // 更新活码
    async updateLiveCode(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const id = parseInt(req.params.id);
            const updateData = req.body;

            const liveCode = await this.liveCodeService.updateLiveCode(
                id, 
                updateData, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: '活码更新成功',
                data: liveCode
            });
        } catch (error) {
            console.error('更新活码失败:', error);
            res.status(400).json({
                error: '更新活码失败',
                message: error.message
            });
        }
    }

    // 删除活码
    async deleteLiveCode(req, res) {
        try {
            const id = parseInt(req.params.id);

            const result = await this.liveCodeService.deleteLiveCode(
                id, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: result.message
            });
        } catch (error) {
            console.error('删除活码失败:', error);
            res.status(400).json({
                error: '删除活码失败',
                message: error.message
            });
        }
    }

    // 复制活码
    async duplicateLiveCode(req, res) {
        try {
            const id = parseInt(req.params.id);

            const liveCode = await this.liveCodeService.duplicateLiveCode(
                id, 
                req.user.id, 
                req.user.role
            );

            res.status(201).json({
                success: true,
                message: '活码复制成功',
                data: liveCode
            });
        } catch (error) {
            console.error('复制活码失败:', error);
            res.status(400).json({
                error: '复制活码失败',
                message: error.message
            });
        }
    }

    // 添加轮询字符串
    async addPollingStrings(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const liveCodeId = parseInt(req.params.id);
            const { strings, batch_name } = req.body;

            const result = await this.liveCodeService.addPollingStrings(
                liveCodeId, 
                strings, 
                batch_name, 
                req.user.id, 
                req.user.role
            );

            res.status(201).json({
                success: true,
                message: result.message,
                data: result
            });
        } catch (error) {
            console.error('添加轮询字符串失败:', error);
            res.status(400).json({
                error: '添加轮询字符串失败',
                message: error.message
            });
        }
    }

    // 更新轮询字符串
    async updatePollingString(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const id = parseInt(req.params.pollingId);
            const updateData = req.body;

            const pollingString = await this.liveCodeService.updatePollingString(
                id, 
                updateData, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: '轮询字符串更新成功',
                data: pollingString
            });
        } catch (error) {
            console.error('更新轮询字符串失败:', error);
            res.status(400).json({
                error: '更新轮询字符串失败',
                message: error.message
            });
        }
    }

    // 删除轮询字符串
    async deletePollingString(req, res) {
        try {
            const id = parseInt(req.params.pollingId);

            const result = await this.liveCodeService.deletePollingString(
                id, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: result.message
            });
        } catch (error) {
            console.error('删除轮询字符串失败:', error);
            res.status(400).json({
                error: '删除轮询字符串失败',
                message: error.message
            });
        }
    }

    // 重新排序轮询字符串
    async reorderPollingStrings(req, res) {
        try {
            const liveCodeId = parseInt(req.params.id);
            const { ordered_ids } = req.body;

            if (!Array.isArray(ordered_ids)) {
                return res.status(400).json({
                    error: '输入验证失败',
                    message: 'ordered_ids必须是数组'
                });
            }

            const result = await this.liveCodeService.reorderPollingStrings(
                liveCodeId, 
                ordered_ids, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: result.message
            });
        } catch (error) {
            console.error('重新排序轮询字符串失败:', error);
            res.status(400).json({
                error: '重新排序轮询字符串失败',
                message: error.message
            });
        }
    }

    // 批量操作轮询字符串
    async batchOperatePollingStrings(req, res) {
        try {
            const liveCodeId = parseInt(req.params.id);
            const { operation, data } = req.body;

            const result = await this.liveCodeService.batchOperatePollingStrings(
                liveCodeId, 
                operation, 
                data, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: result.message
            });
        } catch (error) {
            console.error('批量操作轮询字符串失败:', error);
            res.status(400).json({
                error: '批量操作轮询字符串失败',
                message: error.message
            });
        }
    }

    // 导入轮询字符串
    async importPollingStrings(req, res) {
        try {
            const liveCodeId = parseInt(req.params.id);
            const { text, batch_name, separator } = req.body;

            if (!text) {
                return res.status(400).json({
                    error: '输入验证失败',
                    message: '导入文本不能为空'
                });
            }

            const result = await this.liveCodeService.importPollingStrings(
                liveCodeId, 
                text, 
                { batch_name, separator }, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                message: result.message,
                data: result
            });
        } catch (error) {
            console.error('导入轮询字符串失败:', error);
            res.status(400).json({
                error: '导入轮询字符串失败',
                message: error.message
            });
        }
    }

    // 导出轮询字符串
    async exportPollingStrings(req, res) {
        try {
            const liveCodeId = parseInt(req.params.id);
            const { batch_name, is_active } = req.query;

            const options = {};
            if (batch_name) options.batch_name = batch_name;
            if (is_active !== undefined) options.is_active = parseInt(is_active);

            const result = await this.liveCodeService.exportPollingStrings(
                liveCodeId, 
                options, 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                data: result
            });
        } catch (error) {
            console.error('导出轮询字符串失败:', error);
            res.status(400).json({
                error: '导出轮询字符串失败',
                message: error.message
            });
        }
    }

    // 获取活码统计信息
    async getLiveCodeStats(req, res) {
        try {
            const stats = await this.liveCodeService.getLiveCodeStats(
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                data: stats
            });
        } catch (error) {
            console.error('获取统计信息失败:', error);
            res.status(500).json({
                error: '获取统计信息失败',
                message: error.message
            });
        }
    }

    // 获取热门活码
    async getTopLiveCodes(req, res) {
        try {
            const { limit = 10 } = req.query;

            const topCodes = await this.liveCodeService.getTopLiveCodes(
                parseInt(limit), 
                req.user.id, 
                req.user.role
            );

            res.json({
                success: true,
                data: topCodes
            });
        } catch (error) {
            console.error('获取热门活码失败:', error);
            res.status(500).json({
                error: '获取热门活码失败',
                message: error.message
            });
        }
    }

    // 批量操作方法
    async batchCreateLiveCodes(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const { livecodes } = req.body;
            const userId = req.user.id;
            const results = [];

            for (const liveCodeData of livecodes) {
                try {
                    const liveCode = await this.liveCodeService.createLiveCode({
                        ...liveCodeData,
                        created_by: userId
                    });
                    results.push({ success: true, data: liveCode });
                } catch (error) {
                    results.push({
                        success: false,
                        error: error.message,
                        data: liveCodeData
                    });
                }
            }

            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            res.status(201).json({
                success: true,
                message: `批量创建完成: ${successCount} 成功, ${failCount} 失败`,
                data: {
                    results,
                    summary: {
                        total: results.length,
                        success: successCount,
                        failed: failCount
                    }
                }
            });
        } catch (error) {
            console.error('批量创建活码失败:', error);
            res.status(500).json({
                error: '批量创建活码失败',
                message: error.message
            });
        }
    }

    async batchUpdateLiveCodes(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const { updates } = req.body;
            const userId = req.user.id;
            const userRole = req.user.role;
            const results = [];

            for (const update of updates) {
                try {
                    const liveCode = await this.liveCodeService.updateLiveCode(
                        update.id,
                        update,
                        userId,
                        userRole
                    );
                    results.push({ success: true, data: liveCode });
                } catch (error) {
                    results.push({
                        success: false,
                        error: error.message,
                        id: update.id
                    });
                }
            }

            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            res.json({
                success: true,
                message: `批量更新完成: ${successCount} 成功, ${failCount} 失败`,
                data: {
                    results,
                    summary: {
                        total: results.length,
                        success: successCount,
                        failed: failCount
                    }
                }
            });
        } catch (error) {
            console.error('批量更新活码失败:', error);
            res.status(500).json({
                error: '批量更新活码失败',
                message: error.message
            });
        }
    }

    async batchDeleteLiveCodes(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const { ids } = req.body;
            const userId = req.user.id;
            const userRole = req.user.role;
            const results = [];

            for (const id of ids) {
                try {
                    await this.liveCodeService.deleteLiveCode(id, userId, userRole);
                    results.push({ success: true, id });
                } catch (error) {
                    results.push({
                        success: false,
                        error: error.message,
                        id
                    });
                }
            }

            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            res.json({
                success: true,
                message: `批量删除完成: ${successCount} 成功, ${failCount} 失败`,
                data: {
                    results,
                    summary: {
                        total: results.length,
                        success: successCount,
                        failed: failCount
                    }
                }
            });
        } catch (error) {
            console.error('批量删除活码失败:', error);
            res.status(500).json({
                error: '批量删除活码失败',
                message: error.message
            });
        }
    }

    async batchToggleStatus(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const { ids, is_active } = req.body;
            const userId = req.user.id;
            const userRole = req.user.role;
            const results = [];

            for (const id of ids) {
                try {
                    const liveCode = await this.liveCodeService.updateLiveCode(
                        id,
                        { is_active },
                        userId,
                        userRole
                    );
                    results.push({ success: true, data: liveCode });
                } catch (error) {
                    results.push({
                        success: false,
                        error: error.message,
                        id
                    });
                }
            }

            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            res.json({
                success: true,
                message: `批量${is_active ? '启用' : '禁用'}完成: ${successCount} 成功, ${failCount} 失败`,
                data: {
                    results,
                    summary: {
                        total: results.length,
                        success: successCount,
                        failed: failCount
                    }
                }
            });
        } catch (error) {
            console.error('批量切换状态失败:', error);
            res.status(500).json({
                error: '批量切换状态失败',
                message: error.message
            });
        }
    }

    // 分析相关方法
    async getLiveCodeAnalytics(req, res) {
        try {
            const liveCodeId = parseInt(req.params.id);
            const userId = req.user.id;
            const userRole = req.user.role;
            const { period = 'week' } = req.query;

            // 检查权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            // 获取分析数据（这里需要实现具体的分析逻辑）
            const analytics = {
                total_clicks: 0,
                unique_visitors: 0,
                conversion_rate: 0,
                top_countries: [],
                device_stats: {},
                time_distribution: {},
                period: period
            };

            res.json({
                success: true,
                data: analytics
            });
        } catch (error) {
            console.error('获取活码分析失败:', error);
            res.status(500).json({
                error: '获取活码分析失败',
                message: error.message
            });
        }
    }

    async getLiveCodeTimeline(req, res) {
        try {
            const liveCodeId = parseInt(req.params.id);
            const userId = req.user.id;
            const userRole = req.user.role;
            const { start_date, end_date, granularity = 'day' } = req.query;

            // 检查权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            // 获取时间线数据（这里需要实现具体的时间线逻辑）
            const timeline = [];

            res.json({
                success: true,
                data: {
                    timeline,
                    granularity,
                    start_date,
                    end_date
                }
            });
        } catch (error) {
            console.error('获取活码时间线失败:', error);
            res.status(500).json({
                error: '获取活码时间线失败',
                message: error.message
            });
        }
    }

    async getLiveCodeGeoStats(req, res) {
        try {
            const liveCodeId = parseInt(req.params.id);
            const userId = req.user.id;
            const userRole = req.user.role;

            // 检查权限
            const liveCode = await this.liveCodeService.getLiveCodeById(liveCodeId, userId, userRole);
            if (!liveCode) {
                return res.status(404).json({
                    error: '活码不存在或无权限访问'
                });
            }

            // 获取地理位置统计（这里需要实现具体的地理统计逻辑）
            const geoStats = {
                countries: [],
                cities: [],
                total_countries: 0,
                total_cities: 0
            };

            res.json({
                success: true,
                data: geoStats
            });
        } catch (error) {
            console.error('获取活码地理统计失败:', error);
            res.status(500).json({
                error: '获取活码地理统计失败',
                message: error.message
            });
        }
    }

    // 模板相关方法
    async getTemplates(req, res) {
        try {
            const userId = req.user.id;
            const userRole = req.user.role;

            // 获取模板列表（这里需要实现模板存储逻辑）
            const templates = [];

            res.json({
                success: true,
                data: templates
            });
        } catch (error) {
            console.error('获取模板列表失败:', error);
            res.status(500).json({
                error: '获取模板列表失败',
                message: error.message
            });
        }
    }

    async createTemplate(req, res) {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const { name, template_data } = req.body;
            const userId = req.user.id;

            // 创建模板（这里需要实现模板存储逻辑）
            const template = {
                id: Date.now(),
                name,
                template_data,
                created_by: userId,
                created_at: new Date().toISOString()
            };

            res.status(201).json({
                success: true,
                message: '模板创建成功',
                data: template
            });
        } catch (error) {
            console.error('创建模板失败:', error);
            res.status(500).json({
                error: '创建模板失败',
                message: error.message
            });
        }
    }

    async createFromTemplate(req, res) {
        try {
            const templateId = parseInt(req.params.templateId);
            const userId = req.user.id;
            const customData = req.body;

            // 从模板创建活码（这里需要实现模板应用逻辑）
            const templateData = {}; // 从数据库获取模板数据

            const liveCodeData = {
                ...templateData,
                ...customData,
                created_by: userId
            };

            const liveCode = await this.liveCodeService.createLiveCode(liveCodeData);

            res.status(201).json({
                success: true,
                message: '从模板创建活码成功',
                data: liveCode
            });
        } catch (error) {
            console.error('从模板创建活码失败:', error);
            res.status(500).json({
                error: '从模板创建活码失败',
                message: error.message
            });
        }
    }
}

module.exports = LiveCodeController;
