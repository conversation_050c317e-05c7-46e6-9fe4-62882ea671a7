>本项目需要快速开发
>时间限定：今天晚上辅助我完成
>底层逻辑核心是一个**URL重定向服务**
> 我要创建一个活码系统（或称跳转链接系统），所有的功能我只要核心的，功能越露骨越好，越直白越好。
> **设置目标URL**：这是活码系统的基本功能，我可以随时修改。但目前就只有这一个：https://api.whatsapp.com/send/?phone=
> **实现“轮询字符串”**：我想在目标URL后面添加**使用表单手动输入多批次的、用换行符分隔的字符串**，这需要活码系统支持**轮询跳转**功能。规则是**顺序轮流**
> **按批次更新内容：** 可以分批次地添加或更新目标URL列表，无需每次都更换和重新分享活码。系统会实时读取并使用最新的列表。一起构成了更大的轮询池
> 使用自建 IP 地址解析功能并使用**MaxMind GeoLite2 数据库**
> 预估的流量是：**每天创建活码：** 100+ 个；**每个活码平均访问：** 2000+ 次；**总访问量估算：** 100 * 2000 = **每天至少20万次访问**
> 页面功能，点击**轮询数据**可直接跳转到对应活码的**链接轮询**页面，并自动选中对应活码进行批次数量添加
> 页面功能，点击**访问量**可直接跳转到对应活码的**访问记录**页面，并自动选中对应活码显示访问列表数据：编号, 活码随机码, 活码标题, 访问时间, 字符串, IP位置
> **活码标识符优化：** `$keyMember` 字符串移除 `0`, `O`, `I`, `l`, `1` 这些视觉上容易混淆的字符。标识符长度至少为 8 位
> $keyMember = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz23456789';
> **唯一性检查：** 重申在存入数据库前**进行唯一性检查的必要性**，这是确保系统稳定运行的关键一步。
> **多级权限系统**: 支持超级管理员、Boss、经理三级权限
> **用户管理**: 完整的用户创建、编辑、权限控制功能
> **超级管理员操作审计**: 完整的操作日志记录和审计功能
> 响应式设计，适配不同屏幕尺寸


**其工作原理：**

域名绑定与中间URL生成： 我购买的域名（例如 yourdomain.com）会被用作活码的基础URL。系统会在这个域名下生成一个或多个短小、固定不变的中间URL（例如 yourdomain.com/a1b2c3） 。这个中间URL就是我实际分享出去的“活码”。

映射关系存储： 活码系统会在其数据库中存储中间URL和目标URL之间的映射关系。例如，它会记录 yourdomain.com/a1b2c3 应该跳转到 https://api.whatsapp.com/send/?phone= 

phone= 后面添加轮询数据。

用户点击与请求： 当用户点击我的活码（yourdomain.com/a1b2c3）时，用户的浏览器会向我的域名服务器发送请求。

服务器处理与重定向： 我的域名服务器（或活码系统所在的服务器）接收到请求后，会查询数据库，找到 yourdomain.com/a1b2c3 对应的目标URL（https://api.whatsapp.com/send/?phone=某某）。然后，服务器会向用户的浏览器发送一个重定向指令（通常是HTTP状态码 301 或 302），告诉浏览器去访问新的目标URL。

最终跳转： 用户的浏览器收到重定向指令后，会自动发起对目标URL（https://api.whatsapp.com/send/?phone=）的请求，从而完成跳转。


> [!我需要负责的任务：]
> 1. **域名和服务器：** 购买域名，并准备一台服务器（可以是云服务器，如AWS EC2, Azure VM, Google Cloud VM，或者我自己的物理服务器）。
> 2. **后端开发：** 编写代码实现活码的生成、存储、重定向逻辑、轮询管理以及统计功能。
> 3. **数据库：** 设置和管理数据库来存储活码数据和统计信息。
> 4. **前端界面：** 开发一个用户友好的管理界面，方便我自己操作和管理活码。
> 5. **CDN集成（可选但推荐）：** 考虑如何将我的活码服务接入CDN，以提高全球访问速度。