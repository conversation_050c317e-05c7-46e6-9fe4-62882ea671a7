{"name": "maxmind", "version": "4.3.28", "homepage": "https://github.com/runk/node-maxmind", "description": "IP lookup using Maxmind databases", "keywords": ["maxmind", "mmdb", "geo", "geoip", "geoip2", "geobase", "geo lookup", "ip base", "geocode", "timezone", "asn", "geo lookup", "ip lookup"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> @quafzi <<EMAIL>>", "A<PERSON><PERSON><PERSON> Ameer @afzaalace", "<PERSON> @AndorCS", "<PERSON> @oschwald", "Mariano Facundo Scig<PERSON>o @MarianoFacundoArch"], "dependencies": {"mmdb-lib": "2.2.1", "tiny-lru": "11.3.3"}, "devDependencies": {"@types/ip6addr": "0.2.6", "@types/jest": "29.5.14", "@types/netmask": "2.0.5", "@types/node": "22.15.34", "@types/sinon": "17.0.4", "ip-address": "10.0.1", "ip6addr": "0.2.5", "jest": "29.7.0", "prettier": "3.5.3", "semantic-release": "24.2.6", "sinon": "20.0.0", "ts-jest": "29.3.4", "typescript": "5.8.3"}, "repository": {"type": "git", "url": "https://github.com/runk/node-maxmind.git"}, "bugs": {"mail": "<EMAIL>", "url": "http://github.com/runk/node-maxmind/issues"}, "files": ["lib"], "main": "lib/index.js", "typings": "lib/index.d.ts", "engines": {"node": ">=12", "npm": ">=6"}, "license": "MIT", "scripts": {"build": "rm -rf lib/* && tsc", "format": "prettier --write .", "prepublish": "npm run build", "semantic-release": "semantic-release", "test": "jest"}}