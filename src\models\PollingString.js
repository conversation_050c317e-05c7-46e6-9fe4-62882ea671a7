const Database = require('better-sqlite3');
const path = require('path');

class PollingString {
    constructor() {
        const dbPath = process.env.DATABASE_PATH || path.join(__dirname, '../../data/livecode.db');
        this.db = new Database(dbPath);
    }

    // 创建轮询字符串
    create(pollingData) {
        const { live_code_id, string_value, batch_name } = pollingData;
        
        // 获取下一个排序顺序
        const maxOrderStmt = this.db.prepare(`
            SELECT COALESCE(MAX(sort_order), 0) as max_order 
            FROM polling_strings 
            WHERE live_code_id = ?
        `);
        const { max_order } = maxOrderStmt.get(live_code_id);
        const sort_order = max_order + 1;

        const stmt = this.db.prepare(`
            INSERT INTO polling_strings (live_code_id, string_value, sort_order, batch_name)
            VALUES (?, ?, ?, ?)
        `);

        try {
            const result = stmt.run(live_code_id, string_value, sort_order, batch_name);
            return this.findById(result.lastInsertRowid);
        } catch (error) {
            throw new Error('创建轮询字符串失败: ' + error.message);
        }
    }

    // 批量创建轮询字符串
    createBatch(live_code_id, strings, batch_name = null) {
        const transaction = this.db.transaction((strings) => {
            const results = [];
            for (const string_value of strings) {
                if (string_value.trim()) {
                    const result = this.create({
                        live_code_id,
                        string_value: string_value.trim(),
                        batch_name
                    });
                    results.push(result);
                }
            }
            return results;
        });

        try {
            return transaction(strings);
        } catch (error) {
            throw new Error('批量创建轮询字符串失败: ' + error.message);
        }
    }

    // 根据ID查找轮询字符串
    findById(id) {
        const stmt = this.db.prepare('SELECT * FROM polling_strings WHERE id = ?');
        return stmt.get(id);
    }

    // 根据活码ID获取所有轮询字符串
    findByLiveCodeId(live_code_id, options = {}) {
        let query = 'SELECT * FROM polling_strings WHERE live_code_id = ?';
        const values = [live_code_id];

        if (options.is_active !== undefined) {
            query += ' AND is_active = ?';
            values.push(options.is_active);
        }

        if (options.batch_name) {
            query += ' AND batch_name = ?';
            values.push(options.batch_name);
        }

        query += ' ORDER BY sort_order ASC';

        if (options.limit) {
            query += ' LIMIT ?';
            values.push(options.limit);
        }

        const stmt = this.db.prepare(query);
        return stmt.all(...values);
    }

    // 更新轮询字符串
    update(id, pollingData) {
        const allowedFields = ['string_value', 'sort_order', 'is_active', 'batch_name'];
        const updates = [];
        const values = [];

        for (const [key, value] of Object.entries(pollingData)) {
            if (allowedFields.includes(key)) {
                updates.push(`${key} = ?`);
                values.push(value);
            }
        }

        if (updates.length === 0) {
            throw new Error('没有有效的更新字段');
        }

        updates.push('updated_at = CURRENT_TIMESTAMP');
        values.push(id);

        const stmt = this.db.prepare(`
            UPDATE polling_strings SET ${updates.join(', ')} WHERE id = ?
        `);

        try {
            const result = stmt.run(...values);
            if (result.changes === 0) {
                throw new Error('轮询字符串不存在');
            }
            return this.findById(id);
        } catch (error) {
            throw new Error('更新轮询字符串失败: ' + error.message);
        }
    }

    // 重新排序轮询字符串
    reorder(live_code_id, orderedIds) {
        const transaction = this.db.transaction((orderedIds) => {
            const updateStmt = this.db.prepare(`
                UPDATE polling_strings SET sort_order = ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ? AND live_code_id = ?
            `);

            orderedIds.forEach((id, index) => {
                updateStmt.run(index + 1, id, live_code_id);
            });
        });

        try {
            transaction(orderedIds);
            return true;
        } catch (error) {
            throw new Error('重新排序失败: ' + error.message);
        }
    }

    // 删除轮询字符串
    delete(id) {
        const stmt = this.db.prepare('DELETE FROM polling_strings WHERE id = ?');
        try {
            const result = stmt.run(id);
            return result.changes > 0;
        } catch (error) {
            throw new Error('删除轮询字符串失败: ' + error.message);
        }
    }

    // 批量删除轮询字符串
    deleteBatch(live_code_id, batch_name) {
        const stmt = this.db.prepare(`
            DELETE FROM polling_strings 
            WHERE live_code_id = ? AND batch_name = ?
        `);
        try {
            const result = stmt.run(live_code_id, batch_name);
            return result.changes;
        } catch (error) {
            throw new Error('批量删除轮询字符串失败: ' + error.message);
        }
    }

    // 清空活码的所有轮询字符串
    clearByLiveCodeId(live_code_id) {
        const stmt = this.db.prepare('DELETE FROM polling_strings WHERE live_code_id = ?');
        try {
            const result = stmt.run(live_code_id);
            return result.changes;
        } catch (error) {
            throw new Error('清空轮询字符串失败: ' + error.message);
        }
    }

    // 获取批次列表
    getBatches(live_code_id) {
        const stmt = this.db.prepare(`
            SELECT 
                batch_name,
                COUNT(*) as count,
                COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_count,
                MIN(created_at) as created_at
            FROM polling_strings 
            WHERE live_code_id = ? AND batch_name IS NOT NULL
            GROUP BY batch_name 
            ORDER BY created_at DESC
        `);
        return stmt.all(live_code_id);
    }

    // 获取轮询字符串统计信息
    getStats(live_code_id) {
        const stmt = this.db.prepare(`
            SELECT 
                COUNT(*) as total_strings,
                COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_strings,
                COUNT(DISTINCT batch_name) as total_batches
            FROM polling_strings 
            WHERE live_code_id = ?
        `);
        return stmt.get(live_code_id);
    }

    // 导入轮询字符串（从文本）
    importFromText(live_code_id, text, batch_name = null, separator = '\n') {
        const strings = text.split(separator)
            .map(s => s.trim())
            .filter(s => s.length > 0);

        if (strings.length === 0) {
            throw new Error('没有有效的轮询字符串');
        }

        return this.createBatch(live_code_id, strings, batch_name);
    }

    // 导出轮询字符串为文本
    exportToText(live_code_id, options = {}) {
        const pollingStrings = this.findByLiveCodeId(live_code_id, options);
        return pollingStrings.map(ps => ps.string_value).join('\n');
    }

    // 激活/停用轮询字符串
    toggleActive(id) {
        const current = this.findById(id);
        if (!current) {
            throw new Error('轮询字符串不存在');
        }

        return this.update(id, { is_active: !current.is_active });
    }

    // 批量激活/停用
    toggleActiveBatch(live_code_id, batch_name, is_active) {
        const stmt = this.db.prepare(`
            UPDATE polling_strings 
            SET is_active = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE live_code_id = ? AND batch_name = ?
        `);
        
        try {
            const result = stmt.run(is_active, live_code_id, batch_name);
            return result.changes;
        } catch (error) {
            throw new Error('批量更新状态失败: ' + error.message);
        }
    }

    // 关闭数据库连接
    close() {
        this.db.close();
    }
}

module.exports = PollingString;
