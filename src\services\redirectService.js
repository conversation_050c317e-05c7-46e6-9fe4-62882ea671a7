const LiveCode = require('../models/LiveCode');
const AccessLog = require('../models/AccessLog');
const geoLocationService = require('./geoLocationService');

class RedirectService {
    constructor() {
        this.liveCodeModel = new LiveCode();
        this.accessLogModel = new AccessLog();
    }

    // 处理活码重定向
    async handleRedirect(code, req) {
        try {
            // 查找活码
            const liveCode = this.liveCodeModel.findByCode(code);
            if (!liveCode) {
                throw new Error('活码不存在');
            }

            // 检查活码是否启用
            if (!liveCode.is_active) {
                throw new Error('活码已禁用');
            }

            // 获取下一个轮询字符串
            const pollingString = this.liveCodeModel.getNextPollingString(liveCode.id);
            if (!pollingString) {
                throw new Error('没有可用的轮询字符串');
            }

            // 构建重定向URL
            const redirectUrl = this.buildRedirectUrl(liveCode.base_url, pollingString.string_value);

            // 增加点击次数
            this.liveCodeModel.incrementClickCount(liveCode.id);

            // 获取客户端信息
            const clientInfo = this.extractClientInfo(req);

            // 记录访问日志（异步，不阻塞重定向）
            this.logAccess(liveCode, pollingString, redirectUrl, clientInfo).catch(error => {
                console.error('记录访问日志失败:', error);
            });

            return {
                redirectUrl,
                liveCode,
                pollingString: pollingString.string_value
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 构建重定向URL
    buildRedirectUrl(baseUrl, pollingString) {
        try {
            // 检查基础URL是否为WhatsApp URL
            if (baseUrl.includes('whatsapp.com') || baseUrl.includes('wa.me')) {
                // 处理WhatsApp URL
                return this.buildWhatsAppUrl(baseUrl, pollingString);
            } else {
                // 处理其他URL，将轮询字符串作为查询参数
                const url = new URL(baseUrl);
                url.searchParams.set('ref', pollingString);
                return url.toString();
            }
        } catch (error) {
            // 如果URL解析失败，直接拼接
            const separator = baseUrl.includes('?') ? '&' : '?';
            return `${baseUrl}${separator}ref=${encodeURIComponent(pollingString)}`;
        }
    }

    // 构建WhatsApp URL
    buildWhatsAppUrl(baseUrl, phoneNumber) {
        try {
            // 清理电话号码（移除非数字字符）
            const cleanPhone = phoneNumber.replace(/\D/g, '');
            
            if (!cleanPhone) {
                throw new Error('无效的电话号码');
            }

            // 检查URL类型并构建相应的WhatsApp URL
            if (baseUrl.includes('api.whatsapp.com')) {
                // 使用官方API格式
                const url = new URL(baseUrl);
                url.searchParams.set('phone', cleanPhone);
                return url.toString();
            } else if (baseUrl.includes('wa.me')) {
                // 使用wa.me格式
                return `https://wa.me/${cleanPhone}`;
            } else {
                // 默认使用官方API格式
                return `https://api.whatsapp.com/send/?phone=${cleanPhone}`;
            }
        } catch (error) {
            console.error('构建WhatsApp URL失败:', error);
            // 回退到基础URL
            return baseUrl;
        }
    }

    // 提取客户端信息
    extractClientInfo(req) {
        const ip = this.getClientIP(req);
        const userAgent = req.get('User-Agent') || '';
        
        return {
            ip_address: ip,
            user_agent: userAgent,
            referer: req.get('Referer') || '',
            accept_language: req.get('Accept-Language') || '',
            x_forwarded_for: req.get('X-Forwarded-For') || '',
            x_real_ip: req.get('X-Real-IP') || ''
        };
    }

    // 获取客户端真实IP
    getClientIP(req) {
        return req.ip ||
               req.connection.remoteAddress ||
               req.socket.remoteAddress ||
               (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
               req.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
               req.get('X-Real-IP') ||
               '127.0.0.1';
    }

    // 记录访问日志
    async logAccess(liveCode, pollingString, redirectUrl, clientInfo) {
        try {
            const accessData = {
                live_code_id: liveCode.id,
                live_code: liveCode.code,
                polling_string: pollingString.string_value,
                ip_address: clientInfo.ip_address,
                user_agent: clientInfo.user_agent,
                redirect_url: redirectUrl
            };

            // 如果启用了地理位置追踪，获取地理信息
            if (process.env.ENABLE_GEO_TRACKING === '1') {
                const geoInfo = await this.getGeoLocation(clientInfo.ip_address);
                if (geoInfo) {
                    Object.assign(accessData, geoInfo);
                }
            }

            this.accessLogModel.create(accessData);
        } catch (error) {
            console.error('记录访问日志失败:', error);
            // 不抛出错误，避免影响重定向
        }
    }

    // 获取地理位置信息
    async getGeoLocation(ipAddress) {
        try {
            // 检查是否启用地理位置追踪
            if (process.env.ENABLE_GEO_TRACKING !== '1') {
                return null;
            }

            return await geoLocationService.getGeoLocation(ipAddress);
        } catch (error) {
            console.error('获取地理位置失败:', error);
            return null;
        }
    }

    // 验证活码格式
    isValidLiveCode(code) {
        if (!code || typeof code !== 'string') {
            return false;
        }

        // 检查长度
        const expectedLength = parseInt(process.env.LIVECODE_LENGTH) || 8;
        if (code.length !== expectedLength) {
            return false;
        }

        // 检查字符集
        const charset = process.env.LIVECODE_CHARSET || 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz23456789';
        const charsetRegex = new RegExp(`^[${charset.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]+$`);
        
        return charsetRegex.test(code);
    }

    // 获取重定向统计信息
    async getRedirectStats(options = {}) {
        try {
            const stats = this.accessLogModel.getStats(options);
            return stats;
        } catch (error) {
            throw new Error('获取重定向统计失败: ' + error.message);
        }
    }

    // 获取热门重定向
    async getTopRedirects(limit = 10, options = {}) {
        try {
            const topIPs = this.accessLogModel.getTopIPs({ ...options, limit });
            return topIPs;
        } catch (error) {
            throw new Error('获取热门重定向失败: ' + error.message);
        }
    }

    // 获取地理位置统计
    async getGeoStats(options = {}) {
        try {
            const geoStats = this.accessLogModel.getGeoStats(options);
            return geoStats;
        } catch (error) {
            throw new Error('获取地理位置统计失败: ' + error.message);
        }
    }

    // 获取时间统计
    async getTimeStats(period = 'day', options = {}) {
        try {
            const timeStats = this.accessLogModel.getTimeStats(period, options);
            return timeStats;
        } catch (error) {
            throw new Error('获取时间统计失败: ' + error.message);
        }
    }

    // 检查活码健康状态
    async checkLiveCodeHealth(code) {
        try {
            const liveCode = this.liveCodeModel.findByCode(code);
            if (!liveCode) {
                return {
                    status: 'not_found',
                    message: '活码不存在'
                };
            }

            if (!liveCode.is_active) {
                return {
                    status: 'disabled',
                    message: '活码已禁用'
                };
            }

            // 检查轮询字符串
            const pollingStrings = this.liveCodeModel.db.prepare(`
                SELECT COUNT(*) as count FROM polling_strings 
                WHERE live_code_id = ? AND is_active = 1
            `).get(liveCode.id);

            if (pollingStrings.count === 0) {
                return {
                    status: 'no_polling_strings',
                    message: '没有可用的轮询字符串'
                };
            }

            return {
                status: 'healthy',
                message: '活码状态正常',
                data: {
                    title: liveCode.title,
                    click_count: liveCode.click_count,
                    polling_count: pollingStrings.count,
                    current_poll_index: liveCode.current_poll_index
                }
            };
        } catch (error) {
            return {
                status: 'error',
                message: error.message
            };
        }
    }

    // 预热活码（预加载相关数据）
    async warmupLiveCode(code) {
        try {
            const liveCode = this.liveCodeModel.findByCode(code);
            if (!liveCode) {
                return false;
            }

            // 预加载轮询字符串
            this.liveCodeModel.db.prepare(`
                SELECT * FROM polling_strings 
                WHERE live_code_id = ? AND is_active = 1 
                ORDER BY sort_order ASC
            `).all(liveCode.id);

            return true;
        } catch (error) {
            console.error('预热活码失败:', error);
            return false;
        }
    }

    // 批量检查活码健康状态
    async batchCheckHealth(codes) {
        const results = {};
        
        for (const code of codes) {
            results[code] = await this.checkLiveCodeHealth(code);
        }
        
        return results;
    }
}

module.exports = RedirectService;
