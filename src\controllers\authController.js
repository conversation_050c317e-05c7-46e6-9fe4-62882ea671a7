const AuthService = require('../services/authService');
const { validationResult } = require('express-validator');

class AuthController {
    constructor() {
        this.authService = new AuthService();
    }

    // 用户登录
    async login(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const { username, password } = req.body;

            // 执行登录
            const result = await this.authService.login(username, password);

            res.json({
                success: true,
                message: '登录成功',
                data: result
            });
        } catch (error) {
            console.error('登录失败:', error);
            res.status(401).json({
                error: '登录失败',
                message: error.message
            });
        }
    }

    // 刷新令牌
    async refreshToken(req, res) {
        try {
            const { refreshToken } = req.body;

            if (!refreshToken) {
                return res.status(400).json({
                    error: '缺少刷新令牌',
                    message: '请提供刷新令牌'
                });
            }

            const result = await this.authService.refreshToken(refreshToken);

            res.json({
                success: true,
                message: '令牌刷新成功',
                data: result
            });
        } catch (error) {
            console.error('刷新令牌失败:', error);
            res.status(401).json({
                error: '刷新令牌失败',
                message: error.message
            });
        }
    }

    // 用户注册（管理员功能）
    async register(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const userData = req.body;
            const createdBy = req.user.id;

            // 检查权限：只有超级管理员和Boss可以创建用户
            if (req.user.role !== 'super_admin' && req.user.role !== 'boss') {
                return res.status(403).json({
                    error: '权限不足',
                    message: '只有管理员可以创建用户'
                });
            }

            // Boss只能创建Manager
            if (req.user.role === 'boss' && userData.role !== 'manager') {
                return res.status(403).json({
                    error: '权限不足',
                    message: 'Boss只能创建Manager角色的用户'
                });
            }

            const user = await this.authService.register(userData, createdBy);

            res.status(201).json({
                success: true,
                message: '用户创建成功',
                data: user
            });
        } catch (error) {
            console.error('用户注册失败:', error);
            res.status(400).json({
                error: '用户注册失败',
                message: error.message
            });
        }
    }

    // 修改密码
    async changePassword(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const { oldPassword, newPassword } = req.body;
            const userId = req.user.id;

            const result = await this.authService.changePassword(userId, oldPassword, newPassword);

            res.json({
                success: true,
                message: result.message
            });
        } catch (error) {
            console.error('修改密码失败:', error);
            res.status(400).json({
                error: '修改密码失败',
                message: error.message
            });
        }
    }

    // 重置密码（管理员功能）
    async resetPassword(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const { userId, newPassword } = req.body;
            const adminId = req.user.id;

            const result = await this.authService.resetPassword(userId, newPassword, adminId);

            res.json({
                success: true,
                message: result.message
            });
        } catch (error) {
            console.error('重置密码失败:', error);
            res.status(400).json({
                error: '重置密码失败',
                message: error.message
            });
        }
    }

    // 获取当前用户信息
    async getProfile(req, res) {
        try {
            const userId = req.user.id;
            const user = await this.authService.getUserInfo(userId);

            res.json({
                success: true,
                data: user
            });
        } catch (error) {
            console.error('获取用户信息失败:', error);
            res.status(404).json({
                error: '获取用户信息失败',
                message: error.message
            });
        }
    }

    // 更新用户信息
    async updateProfile(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const userId = req.user.id;
            const updateData = req.body;

            const user = await this.authService.updateUserInfo(userId, updateData);

            res.json({
                success: true,
                message: '用户信息更新成功',
                data: user
            });
        } catch (error) {
            console.error('更新用户信息失败:', error);
            res.status(400).json({
                error: '更新用户信息失败',
                message: error.message
            });
        }
    }

    // 获取用户列表（管理员功能）
    async getUserList(req, res) {
        try {
            const { page = 1, limit = 20, role, is_active, search } = req.query;
            
            const options = {
                limit: parseInt(limit),
                offset: (parseInt(page) - 1) * parseInt(limit)
            };

            if (role) options.role = role;
            if (is_active !== undefined) options.is_active = parseInt(is_active);
            if (search) options.search = search;

            const users = await this.authService.getUserList(req.user.role, options);

            res.json({
                success: true,
                data: users,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit)
                }
            });
        } catch (error) {
            console.error('获取用户列表失败:', error);
            res.status(500).json({
                error: '获取用户列表失败',
                message: error.message
            });
        }
    }

    // 更新用户信息（管理员功能）
    async updateUser(req, res) {
        try {
            // 验证输入
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    error: '输入验证失败',
                    details: errors.array()
                });
            }

            const userId = req.params.id;
            const updateData = req.body;
            const adminId = req.user.id;

            const user = await this.authService.updateUserInfo(userId, updateData, adminId);

            res.json({
                success: true,
                message: '用户信息更新成功',
                data: user
            });
        } catch (error) {
            console.error('更新用户信息失败:', error);
            res.status(400).json({
                error: '更新用户信息失败',
                message: error.message
            });
        }
    }

    // 删除用户（管理员功能）
    async deleteUser(req, res) {
        try {
            const userId = req.params.id;
            const adminId = req.user.id;

            const result = await this.authService.deleteUser(userId, adminId);

            res.json({
                success: true,
                message: result.message
            });
        } catch (error) {
            console.error('删除用户失败:', error);
            res.status(400).json({
                error: '删除用户失败',
                message: error.message
            });
        }
    }

    // 用户登出
    async logout(req, res) {
        try {
            // 在实际应用中，可以将令牌加入黑名单
            // 这里简单返回成功消息
            res.json({
                success: true,
                message: '登出成功'
            });
        } catch (error) {
            console.error('登出失败:', error);
            res.status(500).json({
                error: '登出失败',
                message: error.message
            });
        }
    }

    // 验证令牌
    async verifyToken(req, res) {
        try {
            // 如果能到达这里，说明令牌有效（通过了认证中间件）
            const user = await this.authService.getUserInfo(req.user.id);

            res.json({
                success: true,
                message: '令牌有效',
                data: user
            });
        } catch (error) {
            console.error('验证令牌失败:', error);
            res.status(401).json({
                error: '令牌无效',
                message: error.message
            });
        }
    }
}

module.exports = AuthController;
