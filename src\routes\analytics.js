const express = require('express');
const { query, validationResult } = require('express-validator');

// 导入控制器
const AnalyticsController = require('../controllers/analyticsController');

// 导入中间件
const { 
    authenticateToken, 
    requireManager, 
    requireBoss,
    checkResourceAccess 
} = require('../middleware/auth');

const router = express.Router();

// 所有分析路由都需要认证
router.use(authenticateToken);
router.use(checkResourceAccess);
router.use(requireManager);

// 实例化控制器
const analyticsController = new AnalyticsController();

// 概览统计API
router.get('/overview', (req, res) => {
    analyticsController.getOverview(req, res);
});

router.get('/dashboard', (req, res) => {
    analyticsController.getDashboardData(req, res);
});

// 活码统计API
router.get('/livecodes/stats', 
    query('period').optional().isIn(['hour', 'day', 'week', 'month', 'year']),
    query('start_date').optional().isISO8601(),
    query('end_date').optional().isISO8601(),
    (req, res) => {
        analyticsController.getLiveCodeStats(req, res);
    }
);

router.get('/livecodes/:id/stats', 
    query('period').optional().isIn(['hour', 'day', 'week', 'month', 'year']),
    query('start_date').optional().isISO8601(),
    query('end_date').optional().isISO8601(),
    (req, res) => {
        analyticsController.getLiveCodeDetailStats(req, res);
    }
);

router.get('/livecodes/top', 
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('period').optional().isIn(['day', 'week', 'month', 'year']),
    query('metric').optional().isIn(['clicks', 'unique_visitors', 'conversion_rate']),
    (req, res) => {
        analyticsController.getTopLiveCodes(req, res);
    }
);

// 访问统计API
router.get('/visits/timeline', 
    query('granularity').optional().isIn(['hour', 'day', 'week', 'month']),
    query('start_date').optional().isISO8601(),
    query('end_date').optional().isISO8601(),
    query('live_code_id').optional().isInt({ min: 1 }),
    (req, res) => {
        analyticsController.getVisitTimeline(req, res);
    }
);

router.get('/visits/realtime', (req, res) => {
    analyticsController.getRealtimeVisits(req, res);
});

router.get('/visits/summary', 
    query('period').optional().isIn(['today', 'yesterday', 'week', 'month']),
    (req, res) => {
        analyticsController.getVisitSummary(req, res);
    }
);

// 地理位置统计API
router.get('/geo/countries', 
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('start_date').optional().isISO8601(),
    query('end_date').optional().isISO8601(),
    query('live_code_id').optional().isInt({ min: 1 }),
    (req, res) => {
        analyticsController.getCountryStats(req, res);
    }
);

router.get('/geo/cities', 
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('start_date').optional().isISO8601(),
    query('end_date').optional().isISO8601(),
    query('live_code_id').optional().isInt({ min: 1 }),
    (req, res) => {
        analyticsController.getCityStats(req, res);
    }
);

router.get('/geo/map-data', 
    query('start_date').optional().isISO8601(),
    query('end_date').optional().isISO8601(),
    query('live_code_id').optional().isInt({ min: 1 }),
    (req, res) => {
        analyticsController.getMapData(req, res);
    }
);

// 设备和浏览器统计API
router.get('/devices/stats', 
    query('start_date').optional().isISO8601(),
    query('end_date').optional().isISO8601(),
    query('live_code_id').optional().isInt({ min: 1 }),
    (req, res) => {
        analyticsController.getDeviceStats(req, res);
    }
);

router.get('/browsers/stats', 
    query('start_date').optional().isISO8601(),
    query('end_date').optional().isISO8601(),
    query('live_code_id').optional().isInt({ min: 1 }),
    (req, res) => {
        analyticsController.getBrowserStats(req, res);
    }
);

// 轮询字符串性能统计API
router.get('/polling/performance', 
    query('live_code_id').optional().isInt({ min: 1 }),
    query('start_date').optional().isISO8601(),
    query('end_date').optional().isISO8601(),
    (req, res) => {
        analyticsController.getPollingPerformance(req, res);
    }
);

router.get('/polling/:id/stats', 
    query('period').optional().isIn(['day', 'week', 'month']),
    (req, res) => {
        analyticsController.getPollingStringStats(req, res);
    }
);

// 转换和漏斗分析API
router.get('/conversion/funnel', 
    query('live_code_id').optional().isInt({ min: 1 }),
    query('start_date').optional().isISO8601(),
    query('end_date').optional().isISO8601(),
    (req, res) => {
        analyticsController.getConversionFunnel(req, res);
    }
);

router.get('/conversion/rates', 
    query('period').optional().isIn(['day', 'week', 'month']),
    query('live_code_id').optional().isInt({ min: 1 }),
    (req, res) => {
        analyticsController.getConversionRates(req, res);
    }
);

// 用户行为分析API
router.get('/behavior/flow', 
    query('live_code_id').optional().isInt({ min: 1 }),
    query('start_date').optional().isISO8601(),
    query('end_date').optional().isISO8601(),
    (req, res) => {
        analyticsController.getUserFlow(req, res);
    }
);

router.get('/behavior/retention', 
    query('period').optional().isIn(['day', 'week', 'month']),
    (req, res) => {
        analyticsController.getRetentionAnalysis(req, res);
    }
);

// 报告生成API
router.post('/reports/generate', 
    query('type').isIn(['summary', 'detailed', 'custom']),
    query('format').optional().isIn(['json', 'csv', 'pdf']),
    query('start_date').isISO8601(),
    query('end_date').isISO8601(),
    (req, res) => {
        analyticsController.generateReport(req, res);
    }
);

router.get('/reports/:id', (req, res) => {
    analyticsController.getReport(req, res);
});

router.get('/reports', 
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    (req, res) => {
        analyticsController.getReports(req, res);
    }
);

// 数据导出API（需要Boss权限）
router.get('/export/raw-data', 
    requireBoss,
    query('start_date').isISO8601(),
    query('end_date').isISO8601(),
    query('format').optional().isIn(['json', 'csv']),
    query('live_code_id').optional().isInt({ min: 1 }),
    (req, res) => {
        analyticsController.exportRawData(req, res);
    }
);

router.get('/export/aggregated', 
    requireBoss,
    query('start_date').isISO8601(),
    query('end_date').isISO8601(),
    query('granularity').optional().isIn(['hour', 'day', 'week', 'month']),
    query('format').optional().isIn(['json', 'csv']),
    (req, res) => {
        analyticsController.exportAggregatedData(req, res);
    }
);

// 自定义查询API（需要Boss权限）
router.post('/query/custom', 
    requireBoss,
    (req, res) => {
        analyticsController.executeCustomQuery(req, res);
    }
);

// 数据清理和维护API（需要Boss权限）
router.post('/maintenance/cleanup', 
    requireBoss,
    query('days').optional().isInt({ min: 1, max: 365 }),
    (req, res) => {
        analyticsController.cleanupOldData(req, res);
    }
);

router.post('/maintenance/aggregate', 
    requireBoss,
    (req, res) => {
        analyticsController.aggregateData(req, res);
    }
);

// 错误处理中间件
router.use((error, req, res, next) => {
    console.error('分析路由错误:', error);
    res.status(500).json({
        error: '服务器内部错误',
        message: process.env.NODE_ENV === 'development' ? error.message : '请稍后重试'
    });
});

module.exports = router;
