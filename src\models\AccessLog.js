const Database = require('better-sqlite3');
const path = require('path');

class AccessLog {
    constructor() {
        const dbPath = process.env.DATABASE_PATH || path.join(__dirname, '../../data/livecode.db');
        this.db = new Database(dbPath);
    }

    // 记录访问日志
    create(accessData) {
        const {
            live_code_id,
            live_code,
            polling_string,
            ip_address,
            user_agent,
            country,
            region,
            city,
            latitude,
            longitude,
            redirect_url
        } = accessData;

        const stmt = this.db.prepare(`
            INSERT INTO access_logs (
                live_code_id, live_code, polling_string, ip_address, user_agent,
                country, region, city, latitude, longitude, redirect_url
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        try {
            const result = stmt.run(
                live_code_id, live_code, polling_string, ip_address, user_agent,
                country, region, city, latitude, longitude, redirect_url
            );
            return this.findById(result.lastInsertRowid);
        } catch (error) {
            throw new Error('记录访问日志失败: ' + error.message);
        }
    }

    // 根据ID查找访问日志
    findById(id) {
        const stmt = this.db.prepare('SELECT * FROM access_logs WHERE id = ?');
        return stmt.get(id);
    }

    // 根据活码ID获取访问日志
    findByLiveCodeId(live_code_id, options = {}) {
        let query = `
            SELECT al.*, lc.title as live_code_title 
            FROM access_logs al 
            LEFT JOIN live_codes lc ON al.live_code_id = lc.id 
            WHERE al.live_code_id = ?
        `;
        const values = [live_code_id];

        if (options.start_date) {
            query += ' AND al.access_time >= ?';
            values.push(options.start_date);
        }

        if (options.end_date) {
            query += ' AND al.access_time <= ?';
            values.push(options.end_date);
        }

        if (options.country) {
            query += ' AND al.country = ?';
            values.push(options.country);
        }

        if (options.ip_address) {
            query += ' AND al.ip_address = ?';
            values.push(options.ip_address);
        }

        query += ' ORDER BY al.access_time DESC';

        if (options.limit) {
            query += ' LIMIT ?';
            values.push(options.limit);
        }

        if (options.offset) {
            query += ' OFFSET ?';
            values.push(options.offset);
        }

        const stmt = this.db.prepare(query);
        return stmt.all(...values);
    }

    // 获取所有访问日志
    findAll(options = {}) {
        let query = `
            SELECT al.*, lc.title as live_code_title, lc.code as live_code_code
            FROM access_logs al 
            LEFT JOIN live_codes lc ON al.live_code_id = lc.id
        `;
        const conditions = [];
        const values = [];

        if (options.live_code_id) {
            conditions.push('al.live_code_id = ?');
            values.push(options.live_code_id);
        }

        if (options.start_date) {
            conditions.push('al.access_time >= ?');
            values.push(options.start_date);
        }

        if (options.end_date) {
            conditions.push('al.access_time <= ?');
            values.push(options.end_date);
        }

        if (options.country) {
            conditions.push('al.country = ?');
            values.push(options.country);
        }

        if (options.search) {
            conditions.push('(al.ip_address LIKE ? OR al.country LIKE ? OR al.city LIKE ? OR lc.title LIKE ?)');
            const searchTerm = `%${options.search}%`;
            values.push(searchTerm, searchTerm, searchTerm, searchTerm);
        }

        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        query += ' ORDER BY al.access_time DESC';

        if (options.limit) {
            query += ' LIMIT ?';
            values.push(options.limit);
        }

        if (options.offset) {
            query += ' OFFSET ?';
            values.push(options.offset);
        }

        const stmt = this.db.prepare(query);
        return stmt.all(...values);
    }

    // 获取访问统计信息
    getStats(options = {}) {
        let query = `
            SELECT 
                COUNT(*) as total_visits,
                COUNT(DISTINCT ip_address) as unique_visitors,
                COUNT(DISTINCT live_code_id) as accessed_codes,
                COUNT(DISTINCT country) as countries_count
            FROM access_logs
        `;
        const conditions = [];
        const values = [];

        if (options.live_code_id) {
            conditions.push('live_code_id = ?');
            values.push(options.live_code_id);
        }

        if (options.start_date) {
            conditions.push('access_time >= ?');
            values.push(options.start_date);
        }

        if (options.end_date) {
            conditions.push('access_time <= ?');
            values.push(options.end_date);
        }

        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        const stmt = this.db.prepare(query);
        return stmt.get(...values);
    }

    // 获取地理位置统计
    getGeoStats(options = {}) {
        let query = `
            SELECT 
                country,
                region,
                city,
                COUNT(*) as visit_count,
                COUNT(DISTINCT ip_address) as unique_visitors
            FROM access_logs
        `;
        const conditions = [];
        const values = [];

        if (options.live_code_id) {
            conditions.push('live_code_id = ?');
            values.push(options.live_code_id);
        }

        if (options.start_date) {
            conditions.push('access_time >= ?');
            values.push(options.start_date);
        }

        if (options.end_date) {
            conditions.push('access_time <= ?');
            values.push(options.end_date);
        }

        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        query += ' GROUP BY country, region, city ORDER BY visit_count DESC';

        if (options.limit) {
            query += ' LIMIT ?';
            values.push(options.limit);
        }

        const stmt = this.db.prepare(query);
        return stmt.all(...values);
    }

    // 获取时间统计（按小时/天/月）
    getTimeStats(period = 'day', options = {}) {
        let timeFormat;
        switch (period) {
            case 'hour':
                timeFormat = '%Y-%m-%d %H:00:00';
                break;
            case 'day':
                timeFormat = '%Y-%m-%d';
                break;
            case 'month':
                timeFormat = '%Y-%m';
                break;
            default:
                timeFormat = '%Y-%m-%d';
        }

        let query = `
            SELECT 
                strftime('${timeFormat}', access_time) as time_period,
                COUNT(*) as visit_count,
                COUNT(DISTINCT ip_address) as unique_visitors
            FROM access_logs
        `;
        const conditions = [];
        const values = [];

        if (options.live_code_id) {
            conditions.push('live_code_id = ?');
            values.push(options.live_code_id);
        }

        if (options.start_date) {
            conditions.push('access_time >= ?');
            values.push(options.start_date);
        }

        if (options.end_date) {
            conditions.push('access_time <= ?');
            values.push(options.end_date);
        }

        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        query += ' GROUP BY time_period ORDER BY time_period DESC';

        if (options.limit) {
            query += ' LIMIT ?';
            values.push(options.limit);
        }

        const stmt = this.db.prepare(query);
        return stmt.all(...values);
    }

    // 获取热门IP地址
    getTopIPs(options = {}) {
        let query = `
            SELECT 
                ip_address,
                country,
                city,
                COUNT(*) as visit_count,
                MAX(access_time) as last_visit
            FROM access_logs
        `;
        const conditions = [];
        const values = [];

        if (options.live_code_id) {
            conditions.push('live_code_id = ?');
            values.push(options.live_code_id);
        }

        if (options.start_date) {
            conditions.push('access_time >= ?');
            values.push(options.start_date);
        }

        if (options.end_date) {
            conditions.push('access_time <= ?');
            values.push(options.end_date);
        }

        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        query += ' GROUP BY ip_address ORDER BY visit_count DESC';

        const limit = options.limit || 50;
        query += ' LIMIT ?';
        values.push(limit);

        const stmt = this.db.prepare(query);
        return stmt.all(...values);
    }

    // 删除旧的访问日志
    deleteOldLogs(daysToKeep = 90) {
        const stmt = this.db.prepare(`
            DELETE FROM access_logs 
            WHERE access_time < datetime('now', '-${daysToKeep} days')
        `);
        
        try {
            const result = stmt.run();
            return result.changes;
        } catch (error) {
            throw new Error('删除旧日志失败: ' + error.message);
        }
    }

    // 获取访问日志总数
    getCount(options = {}) {
        let query = 'SELECT COUNT(*) as count FROM access_logs';
        const conditions = [];
        const values = [];

        if (options.live_code_id) {
            conditions.push('live_code_id = ?');
            values.push(options.live_code_id);
        }

        if (options.start_date) {
            conditions.push('access_time >= ?');
            values.push(options.start_date);
        }

        if (options.end_date) {
            conditions.push('access_time <= ?');
            values.push(options.end_date);
        }

        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        const stmt = this.db.prepare(query);
        return stmt.get(...values).count;
    }

    // 关闭数据库连接
    close() {
        this.db.close();
    }
}

module.exports = AccessLog;
