export interface Metadata {
    readonly binaryFormatMajorVersion: number;
    readonly binaryFormatMinorVersion: number;
    readonly buildEpoch: Date;
    readonly databaseType: string;
    readonly languages: string[];
    readonly description: string;
    readonly ipVersion: number;
    readonly nodeCount: number;
    readonly recordSize: number;
    readonly nodeByteSize: number;
    readonly searchTreeSize: number;
    readonly treeDepth: number;
}
export declare const parseMetadata: (db: Buffer) => Metadata;
export declare const isLegacyFormat: (db: <PERSON>uffer) => boolean;
