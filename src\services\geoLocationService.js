const maxmind = require('maxmind');
const path = require('path');
const fs = require('fs');

class GeoLocationService {
    constructor() {
        this.cityLookup = null;
        this.countryLookup = null;
        this.asnLookup = null;
        this.isInitialized = false;
        this.initPromise = null;
    }

    // 初始化MaxMind数据库
    async initialize() {
        if (this.isInitialized) {
            return true;
        }

        if (this.initPromise) {
            return this.initPromise;
        }

        this.initPromise = this._doInitialize();
        return this.initPromise;
    }

    async _doInitialize() {
        try {
            const dataDir = path.join(process.cwd(), 'data', 'maxmind');
            
            // 检查数据目录是否存在
            if (!fs.existsSync(dataDir)) {
                console.warn('MaxMind数据目录不存在，创建目录:', dataDir);
                fs.mkdirSync(dataDir, { recursive: true });
            }

            // 数据库文件路径
            const cityDbPath = path.join(dataDir, 'GeoLite2-City.mmdb');
            const countryDbPath = path.join(dataDir, 'GeoLite2-Country.mmdb');
            const asnDbPath = path.join(dataDir, 'GeoLite2-ASN.mmdb');

            // 检查必需的数据库文件
            const requiredFiles = [
                { path: cityDbPath, name: 'City' },
                { path: countryDbPath, name: 'Country' }
            ];

            const missingFiles = requiredFiles.filter(file => !fs.existsSync(file.path));
            
            if (missingFiles.length > 0) {
                console.warn('缺少MaxMind数据库文件:', missingFiles.map(f => f.name).join(', '));
                console.warn('请从 https://dev.maxmind.com/geoip/geolite2-free-geolocation-data 下载数据库文件');
                console.warn('并将文件放置在:', dataDir);
                
                // 如果没有数据库文件，仍然标记为已初始化，但功能将被禁用
                this.isInitialized = true;
                return false;
            }

            // 加载数据库
            console.log('正在加载MaxMind数据库...');
            
            this.cityLookup = await maxmind.open(cityDbPath);
            console.log('✓ City数据库加载成功');
            
            this.countryLookup = await maxmind.open(countryDbPath);
            console.log('✓ Country数据库加载成功');

            // ASN数据库是可选的
            if (fs.existsSync(asnDbPath)) {
                this.asnLookup = await maxmind.open(asnDbPath);
                console.log('✓ ASN数据库加载成功');
            } else {
                console.warn('ASN数据库文件不存在，ASN查询将被禁用');
            }

            this.isInitialized = true;
            console.log('MaxMind地理位置服务初始化完成');
            return true;

        } catch (error) {
            console.error('MaxMind初始化失败:', error);
            this.isInitialized = true; // 标记为已初始化，避免重复尝试
            return false;
        }
    }

    // 获取IP地理位置信息
    async getGeoLocation(ipAddress) {
        try {
            // 确保服务已初始化
            await this.initialize();

            // 如果没有可用的数据库，返回null
            if (!this.cityLookup || !this.countryLookup) {
                return null;
            }

            // 验证IP地址
            if (!this.isValidIP(ipAddress)) {
                return null;
            }

            // 跳过私有IP地址
            if (this.isPrivateIP(ipAddress)) {
                return {
                    ip_address: ipAddress,
                    country: 'Private',
                    country_code: 'XX',
                    region: 'Private Network',
                    city: 'Private Network',
                    latitude: null,
                    longitude: null,
                    timezone: null,
                    isp: 'Private Network',
                    organization: 'Private Network'
                };
            }

            // 查询城市信息
            const cityData = this.cityLookup.get(ipAddress);
            const countryData = this.countryLookup.get(ipAddress);

            // 构建地理位置信息
            const geoInfo = {
                ip_address: ipAddress,
                country: null,
                country_code: null,
                region: null,
                city: null,
                latitude: null,
                longitude: null,
                timezone: null,
                isp: null,
                organization: null
            };

            // 填充国家信息
            if (countryData && countryData.country) {
                geoInfo.country = countryData.country.names?.zh_CN || countryData.country.names?.en || null;
                geoInfo.country_code = countryData.country.iso_code || null;
            }

            // 填充城市和地区信息
            if (cityData) {
                if (cityData.country) {
                    geoInfo.country = geoInfo.country || cityData.country.names?.zh_CN || cityData.country.names?.en || null;
                    geoInfo.country_code = geoInfo.country_code || cityData.country.iso_code || null;
                }

                if (cityData.subdivisions && cityData.subdivisions.length > 0) {
                    geoInfo.region = cityData.subdivisions[0].names?.zh_CN || cityData.subdivisions[0].names?.en || null;
                }

                if (cityData.city) {
                    geoInfo.city = cityData.city.names?.zh_CN || cityData.city.names?.en || null;
                }

                if (cityData.location) {
                    geoInfo.latitude = cityData.location.latitude || null;
                    geoInfo.longitude = cityData.location.longitude || null;
                    geoInfo.timezone = cityData.location.time_zone || null;
                }
            }

            // 查询ASN信息（如果可用）
            if (this.asnLookup) {
                try {
                    const asnData = this.asnLookup.get(ipAddress);
                    if (asnData) {
                        geoInfo.isp = asnData.autonomous_system_organization || null;
                        geoInfo.organization = asnData.autonomous_system_organization || null;
                    }
                } catch (asnError) {
                    // ASN查询失败不影响其他信息
                    console.warn('ASN查询失败:', asnError.message);
                }
            }

            return geoInfo;

        } catch (error) {
            console.error('地理位置查询失败:', error);
            return null;
        }
    }

    // 批量查询地理位置
    async batchGetGeoLocation(ipAddresses) {
        const results = {};
        
        for (const ip of ipAddresses) {
            results[ip] = await this.getGeoLocation(ip);
        }
        
        return results;
    }

    // 验证IP地址格式
    isValidIP(ip) {
        if (!ip || typeof ip !== 'string') {
            return false;
        }

        // IPv4正则表达式
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        
        // IPv6正则表达式（简化版）
        const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;

        return ipv4Regex.test(ip) || ipv6Regex.test(ip);
    }

    // 检查是否为私有IP地址
    isPrivateIP(ip) {
        if (!this.isValidIP(ip)) {
            return false;
        }

        // IPv6本地地址
        if (ip === '::1' || ip.startsWith('fe80:') || ip.startsWith('fc00:') || ip.startsWith('fd00:')) {
            return true;
        }

        // IPv4私有地址
        const parts = ip.split('.').map(Number);
        if (parts.length !== 4) {
            return false;
        }

        // 10.0.0.0/8
        if (parts[0] === 10) {
            return true;
        }

        // **********/12
        if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) {
            return true;
        }

        // ***********/16
        if (parts[0] === 192 && parts[1] === 168) {
            return true;
        }

        // *********/8 (localhost)
        if (parts[0] === 127) {
            return true;
        }

        // ***********/16 (link-local)
        if (parts[0] === 169 && parts[1] === 254) {
            return true;
        }

        return false;
    }

    // 获取服务状态
    getStatus() {
        return {
            initialized: this.isInitialized,
            city_db_available: !!this.cityLookup,
            country_db_available: !!this.countryLookup,
            asn_db_available: !!this.asnLookup,
            enabled: process.env.ENABLE_GEO_TRACKING === '1'
        };
    }

    // 测试地理位置服务
    async testService() {
        try {
            await this.initialize();

            const testIPs = [
                '*******',      // Google DNS
                '*******',      // Cloudflare DNS
                '***************', // 114 DNS (中国)
                '127.0.0.1',    // localhost
                '***********'   // 私有IP
            ];

            const results = {};
            for (const ip of testIPs) {
                results[ip] = await this.getGeoLocation(ip);
            }

            return {
                status: 'success',
                service_status: this.getStatus(),
                test_results: results
            };

        } catch (error) {
            return {
                status: 'error',
                message: error.message,
                service_status: this.getStatus()
            };
        }
    }

    // 清理资源
    close() {
        if (this.cityLookup) {
            // MaxMind Reader没有显式的close方法，让GC处理
            this.cityLookup = null;
        }
        if (this.countryLookup) {
            this.countryLookup = null;
        }
        if (this.asnLookup) {
            this.asnLookup = null;
        }
        this.isInitialized = false;
        this.initPromise = null;
    }

    // 获取数据库信息
    async getDatabaseInfo() {
        await this.initialize();

        const info = {
            status: this.getStatus(),
            databases: {}
        };

        const dataDir = path.join(process.cwd(), 'data', 'maxmind');
        const dbFiles = [
            { name: 'City', file: 'GeoLite2-City.mmdb' },
            { name: 'Country', file: 'GeoLite2-Country.mmdb' },
            { name: 'ASN', file: 'GeoLite2-ASN.mmdb' }
        ];

        for (const db of dbFiles) {
            const filePath = path.join(dataDir, db.file);
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                info.databases[db.name] = {
                    exists: true,
                    size: stats.size,
                    modified: stats.mtime,
                    path: filePath
                };
            } else {
                info.databases[db.name] = {
                    exists: false,
                    path: filePath
                };
            }
        }

        return info;
    }
}

// 创建单例实例
const geoLocationService = new GeoLocationService();

module.exports = geoLocationService;
