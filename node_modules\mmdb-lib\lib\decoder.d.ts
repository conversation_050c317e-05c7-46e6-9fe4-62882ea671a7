import { Cache } from './types';
interface Cursor {
    value: any;
    offset: number;
}
export default class Decoder {
    telemetry: Record<string, any>;
    private db;
    private baseOffset;
    private cache;
    constructor(db: Buffer, baseOffset?: number, cache?: Cache);
    decode(offset: number): Cursor;
    decodeFast(offset: number): any;
    private decodeByType;
    private sizeFromCtrlByte;
    private decodeBytes;
    private decodePointer;
    private decodeArray;
    private decodeBoolean;
    private decodeDouble;
    private decodeFloat;
    private decodeMap;
    private decodeInt32;
    private decodeUint;
    private decodeString;
    private decodeBigUint;
}
export {};
