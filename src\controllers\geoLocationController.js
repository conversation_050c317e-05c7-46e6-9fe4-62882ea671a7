const geoLocationService = require('../services/geoLocationService');

class GeoLocationController {
    constructor() {
        this.geoLocationService = geoLocationService;
    }

    // 获取地理位置服务状态
    async getStatus(req, res) {
        try {
            const status = this.geoLocationService.getStatus();
            
            res.json({
                success: true,
                data: status
            });
        } catch (error) {
            console.error('获取地理位置服务状态失败:', error);
            res.status(500).json({
                error: '获取服务状态失败',
                message: error.message
            });
        }
    }

    // 获取数据库信息
    async getDatabaseInfo(req, res) {
        try {
            const info = await this.geoLocationService.getDatabaseInfo();
            
            res.json({
                success: true,
                data: info
            });
        } catch (error) {
            console.error('获取数据库信息失败:', error);
            res.status(500).json({
                error: '获取数据库信息失败',
                message: error.message
            });
        }
    }

    // 测试地理位置服务
    async testService(req, res) {
        try {
            const testResult = await this.geoLocationService.testService();
            
            res.json({
                success: testResult.status === 'success',
                data: testResult
            });
        } catch (error) {
            console.error('测试地理位置服务失败:', error);
            res.status(500).json({
                error: '测试服务失败',
                message: error.message
            });
        }
    }

    // 查询单个IP地理位置
    async lookupIP(req, res) {
        try {
            const { ip } = req.params;

            if (!ip) {
                return res.status(400).json({
                    error: '参数错误',
                    message: 'IP地址不能为空'
                });
            }

            // 验证IP格式
            if (!this.geoLocationService.isValidIP(ip)) {
                return res.status(400).json({
                    error: '参数错误',
                    message: 'IP地址格式不正确'
                });
            }

            const geoInfo = await this.geoLocationService.getGeoLocation(ip);

            res.json({
                success: true,
                data: geoInfo
            });
        } catch (error) {
            console.error('IP地理位置查询失败:', error);
            res.status(500).json({
                error: 'IP查询失败',
                message: error.message
            });
        }
    }

    // 批量查询IP地理位置
    async batchLookupIPs(req, res) {
        try {
            const { ips } = req.body;

            if (!Array.isArray(ips) || ips.length === 0) {
                return res.status(400).json({
                    error: '参数错误',
                    message: 'ips必须是非空数组'
                });
            }

            if (ips.length > 100) {
                return res.status(400).json({
                    error: '参数错误',
                    message: '一次最多查询100个IP地址'
                });
            }

            // 验证所有IP格式
            const invalidIPs = ips.filter(ip => !this.geoLocationService.isValidIP(ip));
            if (invalidIPs.length > 0) {
                return res.status(400).json({
                    error: 'IP格式错误',
                    message: `以下IP地址格式不正确: ${invalidIPs.join(', ')}`
                });
            }

            const results = await this.geoLocationService.batchGetGeoLocation(ips);

            res.json({
                success: true,
                data: results
            });
        } catch (error) {
            console.error('批量IP地理位置查询失败:', error);
            res.status(500).json({
                error: '批量查询失败',
                message: error.message
            });
        }
    }

    // 初始化地理位置服务
    async initializeService(req, res) {
        try {
            const success = await this.geoLocationService.initialize();
            
            res.json({
                success: success,
                message: success ? '地理位置服务初始化成功' : '地理位置服务初始化失败，请检查数据库文件',
                data: this.geoLocationService.getStatus()
            });
        } catch (error) {
            console.error('初始化地理位置服务失败:', error);
            res.status(500).json({
                error: '初始化失败',
                message: error.message
            });
        }
    }

    // 获取当前请求的地理位置信息
    async getCurrentLocation(req, res) {
        try {
            // 获取客户端IP
            const ip = req.ip ||
                      req.connection.remoteAddress ||
                      req.socket.remoteAddress ||
                      (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
                      req.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
                      req.get('X-Real-IP') ||
                      '127.0.0.1';

            const geoInfo = await this.geoLocationService.getGeoLocation(ip);

            res.json({
                success: true,
                data: {
                    detected_ip: ip,
                    geo_info: geoInfo,
                    headers: {
                        'x-forwarded-for': req.get('X-Forwarded-For'),
                        'x-real-ip': req.get('X-Real-IP'),
                        'user-agent': req.get('User-Agent')
                    }
                }
            });
        } catch (error) {
            console.error('获取当前位置失败:', error);
            res.status(500).json({
                error: '获取当前位置失败',
                message: error.message
            });
        }
    }

    // 验证IP地址格式
    async validateIP(req, res) {
        try {
            const { ip } = req.params;

            if (!ip) {
                return res.status(400).json({
                    error: '参数错误',
                    message: 'IP地址不能为空'
                });
            }

            const isValid = this.geoLocationService.isValidIP(ip);
            const isPrivate = isValid ? this.geoLocationService.isPrivateIP(ip) : false;

            res.json({
                success: true,
                data: {
                    ip: ip,
                    is_valid: isValid,
                    is_private: isPrivate,
                    type: isValid ? (ip.includes(':') ? 'IPv6' : 'IPv4') : 'Invalid'
                }
            });
        } catch (error) {
            console.error('IP验证失败:', error);
            res.status(500).json({
                error: 'IP验证失败',
                message: error.message
            });
        }
    }

    // 获取地理位置统计信息
    async getGeoStats(req, res) {
        try {
            // 这个功能需要从AccessLog模型中获取统计信息
            // 这里提供一个基础实现，可以根据需要扩展
            
            const { 
                start_date, 
                end_date, 
                limit = 50 
            } = req.query;

            // 这里应该调用AccessLog模型的相关方法
            // 暂时返回示例数据结构
            const stats = {
                total_requests: 0,
                unique_countries: 0,
                unique_cities: 0,
                top_countries: [],
                top_cities: [],
                top_regions: []
            };

            res.json({
                success: true,
                data: stats,
                message: '地理位置统计功能需要与AccessLog模型集成'
            });
        } catch (error) {
            console.error('获取地理位置统计失败:', error);
            res.status(500).json({
                error: '获取统计失败',
                message: error.message
            });
        }
    }
}

module.exports = GeoLocationController;
