const express = require('express');
const { body } = require('express-validator');

// 导入各个API模块
const authRoutes = require('./auth');
const liveCodeRoutes = require('./livecode');
const redirectRoutes = require('./redirect');
const geoLocationRoutes = require('./geolocation');

// 导入中间件
const { 
    authenticateToken, 
    optionalAuth,
    requireSuperAdmin, 
    requireBoss, 
    requireManager,
    checkResourceAccess 
} = require('../middleware/auth');

const router = express.Router();

// API版本信息
router.get('/', (req, res) => {
    res.json({
        name: '活码管理平台 API',
        version: '1.0.0',
        description: '高性能URL重定向服务API',
        endpoints: {
            auth: '/api/v1/auth',
            livecodes: '/api/v1/livecodes',
            redirect: '/api/v1/redirect',
            geolocation: '/api/v1/geolocation',
            analytics: '/api/v1/analytics'
        },
        documentation: '/api/docs',
        status: 'operational'
    });
});

// API健康检查
router.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.version
    });
});

// API状态检查（需要认证）
router.get('/status', authenticateToken, requireBoss, (req, res) => {
    res.json({
        status: 'operational',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment: process.env.NODE_ENV,
        database: 'connected', // 这里可以添加数据库连接检查
        services: {
            authentication: 'operational',
            geolocation: process.env.ENABLE_GEO_TRACKING === '1' ? 'operational' : 'disabled',
            redirect: 'operational'
        }
    });
});

// V1 API路由
const v1Router = express.Router();

// 认证相关API
v1Router.use('/auth', authRoutes);

// 活码管理API（需要认证）
v1Router.use('/livecodes', liveCodeRoutes);

// 重定向统计API（从redirect路由中提取API部分）
v1Router.use('/redirect', redirectRoutes);

// 地理位置API
v1Router.use('/geolocation', geoLocationRoutes);

// 分析和报告API
v1Router.get('/analytics/overview', authenticateToken, requireManager, async (req, res) => {
    try {
        // 这里将在后续任务中实现完整的分析功能
        const overview = {
            total_livecodes: 0,
            total_clicks: 0,
            total_users: 0,
            active_livecodes: 0,
            today_clicks: 0,
            top_countries: [],
            recent_activity: []
        };

        res.json({
            success: true,
            data: overview
        });
    } catch (error) {
        console.error('获取分析概览失败:', error);
        res.status(500).json({
            error: '获取分析概览失败',
            message: error.message
        });
    }
});

// 用户管理API（扩展认证API）
v1Router.get('/users/profile', authenticateToken, (req, res) => {
    res.json({
        success: true,
        data: {
            id: req.user.id,
            username: req.user.username,
            email: req.user.email,
            role: req.user.role,
            is_active: req.user.is_active,
            created_at: req.user.created_at,
            last_login: req.user.last_login
        }
    });
});

v1Router.get('/users/permissions', authenticateToken, (req, res) => {
    const permissions = {
        can_create_livecodes: true,
        can_manage_own_livecodes: true,
        can_manage_all_livecodes: ['super_admin', 'boss'].includes(req.user.role),
        can_manage_users: ['super_admin', 'boss'].includes(req.user.role),
        can_view_analytics: true,
        can_view_global_analytics: ['super_admin', 'boss'].includes(req.user.role),
        can_manage_geolocation: ['super_admin', 'boss'].includes(req.user.role),
        can_export_data: ['super_admin', 'boss'].includes(req.user.role)
    };

    res.json({
        success: true,
        data: permissions
    });
});

// 系统配置API（仅超级管理员）
v1Router.get('/system/config', authenticateToken, requireSuperAdmin, (req, res) => {
    const config = {
        livecode_length: process.env.LIVECODE_LENGTH || 8,
        livecode_charset: process.env.LIVECODE_CHARSET || 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz23456789',
        geo_tracking_enabled: process.env.ENABLE_GEO_TRACKING === '1',
        rate_limit_window: process.env.RATE_LIMIT_WINDOW_MS || 900000,
        rate_limit_max: process.env.RATE_LIMIT_MAX_REQUESTS || 100,
        jwt_expires_in: process.env.JWT_EXPIRES_IN || '24h'
    };

    res.json({
        success: true,
        data: config
    });
});

v1Router.put('/system/config', 
    authenticateToken, 
    requireSuperAdmin,
    body('livecode_length').optional().isInt({ min: 6, max: 12 }),
    body('geo_tracking_enabled').optional().isBoolean(),
    body('rate_limit_max').optional().isInt({ min: 10, max: 10000 }),
    async (req, res) => {
        try {
            // 这里应该实现系统配置更新逻辑
            // 暂时返回成功响应
            res.json({
                success: true,
                message: '系统配置更新成功',
                data: req.body
            });
        } catch (error) {
            console.error('更新系统配置失败:', error);
            res.status(500).json({
                error: '更新系统配置失败',
                message: error.message
            });
        }
    }
);

// 数据导出API
v1Router.get('/export/livecodes', authenticateToken, requireBoss, async (req, res) => {
    try {
        const { format = 'json', start_date, end_date } = req.query;
        
        // 这里将在后续任务中实现完整的导出功能
        res.json({
            success: true,
            message: '数据导出功能将在后续版本中实现',
            requested_format: format,
            filters: { start_date, end_date }
        });
    } catch (error) {
        console.error('导出活码数据失败:', error);
        res.status(500).json({
            error: '导出数据失败',
            message: error.message
        });
    }
});

v1Router.get('/export/analytics', authenticateToken, requireBoss, async (req, res) => {
    try {
        const { format = 'json', start_date, end_date, live_code_id } = req.query;
        
        // 这里将在后续任务中实现完整的导出功能
        res.json({
            success: true,
            message: '分析数据导出功能将在后续版本中实现',
            requested_format: format,
            filters: { start_date, end_date, live_code_id }
        });
    } catch (error) {
        console.error('导出分析数据失败:', error);
        res.status(500).json({
            error: '导出数据失败',
            message: error.message
        });
    }
});

// 批量操作API
v1Router.post('/batch/livecodes/delete', 
    authenticateToken, 
    requireBoss,
    body('livecode_ids').isArray({ min: 1, max: 100 }),
    body('livecode_ids.*').isInt({ min: 1 }),
    async (req, res) => {
        try {
            const { livecode_ids } = req.body;
            
            // 这里将在后续任务中实现批量删除功能
            res.json({
                success: true,
                message: `批量删除 ${livecode_ids.length} 个活码的功能将在后续版本中实现`,
                affected_count: livecode_ids.length
            });
        } catch (error) {
            console.error('批量删除活码失败:', error);
            res.status(500).json({
                error: '批量删除失败',
                message: error.message
            });
        }
    }
);

// 挂载v1路由
router.use('/v1', v1Router);

// API文档路由（简单版本）
router.get('/docs', (req, res) => {
    res.json({
        title: '活码管理平台 API 文档',
        version: '1.0.0',
        base_url: '/api/v1',
        authentication: {
            type: 'Bearer Token (JWT)',
            header: 'Authorization: Bearer <token>',
            login_endpoint: '/api/v1/auth/login'
        },
        endpoints: {
            authentication: {
                'POST /auth/login': '用户登录',
                'POST /auth/logout': '用户登出',
                'POST /auth/refresh': '刷新令牌',
                'GET /auth/profile': '获取用户信息',
                'PUT /auth/profile': '更新用户信息',
                'POST /auth/change-password': '修改密码'
            },
            livecodes: {
                'GET /livecodes': '获取活码列表',
                'POST /livecodes': '创建活码',
                'GET /livecodes/:id': '获取活码详情',
                'PUT /livecodes/:id': '更新活码',
                'DELETE /livecodes/:id': '删除活码',
                'POST /livecodes/:id/duplicate': '复制活码'
            },
            polling_strings: {
                'POST /livecodes/:id/polling-strings': '添加轮询字符串',
                'PUT /livecodes/:id/polling-strings/:pollingId': '更新轮询字符串',
                'DELETE /livecodes/:id/polling-strings/:pollingId': '删除轮询字符串',
                'POST /livecodes/:id/polling-strings/import': '导入轮询字符串',
                'GET /livecodes/:id/polling-strings/export': '导出轮询字符串'
            },
            redirect: {
                'GET /:code': '活码重定向',
                'GET /:code/preview': '预览活码信息',
                'GET /:code/health': '检查活码健康状态'
            },
            analytics: {
                'GET /analytics/overview': '获取分析概览',
                'GET /redirect/api/stats': '获取重定向统计',
                'GET /redirect/api/stats/geo': '获取地理位置统计',
                'GET /redirect/api/stats/time': '获取时间统计'
            }
        },
        response_format: {
            success: {
                success: true,
                data: '响应数据',
                message: '可选的消息'
            },
            error: {
                error: '错误类型',
                message: '错误描述',
                details: '可选的详细信息'
            }
        }
    });
});

// 错误处理中间件
router.use((error, req, res, next) => {
    console.error('API路由错误:', error);
    res.status(500).json({
        error: '服务器内部错误',
        message: process.env.NODE_ENV === 'development' ? error.message : '请稍后重试'
    });
});

module.exports = router;
