const express = require('express');
const { body, validationResult } = require('express-validator');

// 导入服务和控制器
const LiveCodeService = require('../services/liveCodeService');
const PollingStringController = require('../controllers/pollingStringController');

// 导入中间件
const { 
    authenticateToken, 
    requireManager, 
    checkResourceAccess 
} = require('../middleware/auth');

const router = express.Router();

// 所有轮询字符串路由都需要认证
router.use(authenticateToken);
router.use(checkResourceAccess);
router.use(requireManager);

// 实例化控制器
const pollingStringController = new PollingStringController();

// 获取轮询字符串列表
router.get('/livecode/:liveCodeId/polling-strings', (req, res) => {
    pollingStringController.getPollingStrings(req, res);
});

// 添加轮询字符串
router.post('/livecode/:liveCodeId/polling-strings', 
    body('phone_number').isMobilePhone('any').withMessage('手机号格式不正确'),
    body('display_name').optional().isLength({ max: 100 }).withMessage('显示名称不能超过100字符'),
    body('weight').optional().isInt({ min: 1, max: 100 }).withMessage('权重必须在1-100之间'),
    body('is_active').optional().isBoolean().withMessage('状态必须是布尔值'),
    (req, res) => {
        pollingStringController.addPollingString(req, res);
    }
);

// 更新轮询字符串
router.put('/livecode/:liveCodeId/polling-strings/:pollingId', 
    body('phone_number').optional().isMobilePhone('any').withMessage('手机号格式不正确'),
    body('display_name').optional().isLength({ max: 100 }).withMessage('显示名称不能超过100字符'),
    body('weight').optional().isInt({ min: 1, max: 100 }).withMessage('权重必须在1-100之间'),
    body('is_active').optional().isBoolean().withMessage('状态必须是布尔值'),
    (req, res) => {
        pollingStringController.updatePollingString(req, res);
    }
);

// 删除轮询字符串
router.delete('/livecode/:liveCodeId/polling-strings/:pollingId', (req, res) => {
    pollingStringController.deletePollingString(req, res);
});

// 批量操作API
router.post('/livecode/:liveCodeId/polling-strings/batch/add', 
    body('polling_strings').isArray({ min: 1, max: 100 }).withMessage('polling_strings必须是包含1-100个元素的数组'),
    body('polling_strings.*.phone_number').isMobilePhone('any').withMessage('手机号格式不正确'),
    body('polling_strings.*.display_name').optional().isLength({ max: 100 }).withMessage('显示名称不能超过100字符'),
    body('polling_strings.*.weight').optional().isInt({ min: 1, max: 100 }).withMessage('权重必须在1-100之间'),
    (req, res) => {
        pollingStringController.batchAddPollingStrings(req, res);
    }
);

router.post('/livecode/:liveCodeId/polling-strings/batch/update', 
    body('updates').isArray({ min: 1, max: 100 }).withMessage('updates必须是包含1-100个元素的数组'),
    body('updates.*.id').isInt({ min: 1 }).withMessage('轮询字符串ID必须是正整数'),
    (req, res) => {
        pollingStringController.batchUpdatePollingStrings(req, res);
    }
);

router.post('/livecode/:liveCodeId/polling-strings/batch/delete', 
    body('ids').isArray({ min: 1, max: 100 }).withMessage('ids必须是包含1-100个元素的数组'),
    body('ids.*').isInt({ min: 1 }).withMessage('轮询字符串ID必须是正整数'),
    (req, res) => {
        pollingStringController.batchDeletePollingStrings(req, res);
    }
);

router.post('/livecode/:liveCodeId/polling-strings/batch/toggle-status', 
    body('ids').isArray({ min: 1, max: 100 }).withMessage('ids必须是包含1-100个元素的数组'),
    body('ids.*').isInt({ min: 1 }).withMessage('轮询字符串ID必须是正整数'),
    body('is_active').isBoolean().withMessage('is_active必须是布尔值'),
    (req, res) => {
        pollingStringController.batchToggleStatus(req, res);
    }
);

// 排序和权重管理
router.post('/livecode/:liveCodeId/polling-strings/reorder', 
    body('order').isArray({ min: 1 }).withMessage('order必须是数组'),
    body('order.*').isInt({ min: 1 }).withMessage('轮询字符串ID必须是正整数'),
    (req, res) => {
        pollingStringController.reorderPollingStrings(req, res);
    }
);

router.post('/livecode/:liveCodeId/polling-strings/auto-weight', 
    body('strategy').isIn(['equal', 'random', 'performance']).withMessage('策略必须是equal、random或performance'),
    (req, res) => {
        pollingStringController.autoAssignWeights(req, res);
    }
);

// 导入导出API
router.post('/livecode/:liveCodeId/polling-strings/import', 
    body('format').isIn(['csv', 'json', 'txt']).withMessage('格式必须是csv、json或txt'),
    body('data').notEmpty().withMessage('导入数据不能为空'),
    body('options').optional().isObject().withMessage('选项必须是对象'),
    (req, res) => {
        pollingStringController.importPollingStrings(req, res);
    }
);

router.get('/livecode/:liveCodeId/polling-strings/export', (req, res) => {
    pollingStringController.exportPollingStrings(req, res);
});

router.get('/livecode/:liveCodeId/polling-strings/template', (req, res) => {
    pollingStringController.getImportTemplate(req, res);
});

// 统计和分析API
router.get('/livecode/:liveCodeId/polling-strings/stats', (req, res) => {
    pollingStringController.getPollingStats(req, res);
});

router.get('/livecode/:liveCodeId/polling-strings/performance', (req, res) => {
    pollingStringController.getPerformanceStats(req, res);
});

router.get('/livecode/:liveCodeId/polling-strings/:pollingId/analytics', (req, res) => {
    pollingStringController.getPollingStringAnalytics(req, res);
});

// 测试和验证API
router.post('/livecode/:liveCodeId/polling-strings/test', 
    body('phone_number').isMobilePhone('any').withMessage('手机号格式不正确'),
    (req, res) => {
        pollingStringController.testPollingString(req, res);
    }
);

router.post('/livecode/:liveCodeId/polling-strings/validate', 
    body('polling_strings').isArray({ min: 1, max: 100 }).withMessage('polling_strings必须是包含1-100个元素的数组'),
    (req, res) => {
        pollingStringController.validatePollingStrings(req, res);
    }
);

// 轮询逻辑管理
router.get('/livecode/:liveCodeId/polling-strings/next', (req, res) => {
    pollingStringController.getNextPollingString(req, res);
});

router.post('/livecode/:liveCodeId/polling-strings/reset-counter', (req, res) => {
    pollingStringController.resetPollingCounter(req, res);
});

router.get('/livecode/:liveCodeId/polling-strings/current-status', (req, res) => {
    pollingStringController.getCurrentPollingStatus(req, res);
});

// 备份和恢复API
router.post('/livecode/:liveCodeId/polling-strings/backup', (req, res) => {
    pollingStringController.createBackup(req, res);
});

router.post('/livecode/:liveCodeId/polling-strings/restore', 
    body('backup_id').notEmpty().withMessage('备份ID不能为空'),
    (req, res) => {
        pollingStringController.restoreFromBackup(req, res);
    }
);

router.get('/livecode/:liveCodeId/polling-strings/backups', (req, res) => {
    pollingStringController.getBackups(req, res);
});

// 错误处理中间件
router.use((error, req, res, next) => {
    console.error('轮询字符串路由错误:', error);
    res.status(500).json({
        error: '服务器内部错误',
        message: process.env.NODE_ENV === 'development' ? error.message : '请稍后重试'
    });
});

module.exports = router;
