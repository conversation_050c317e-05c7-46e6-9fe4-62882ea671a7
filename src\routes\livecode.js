const express = require('express');
const { body } = require('express-validator');
const LiveCodeController = require('../controllers/liveCodeController');
const { 
    authenticateToken, 
    requireManager, 
    checkResourceAccess 
} = require('../middleware/auth');

const router = express.Router();
const liveCodeController = new LiveCodeController();

// 输入验证规则
const createLiveCodeValidation = [
    body('title')
        .notEmpty()
        .withMessage('活码标题不能为空')
        .isLength({ min: 1, max: 200 })
        .withMessage('活码标题长度必须在1-200个字符之间'),
    body('description')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('活码描述长度不能超过1000个字符'),
    body('base_url')
        .notEmpty()
        .withMessage('基础URL不能为空')
        .isURL()
        .withMessage('基础URL格式不正确'),
    body('polling_strings')
        .optional()
        .isArray()
        .withMessage('轮询字符串必须是数组')
];

const updateLiveCodeValidation = [
    body('title')
        .optional()
        .notEmpty()
        .withMessage('活码标题不能为空')
        .isLength({ min: 1, max: 200 })
        .withMessage('活码标题长度必须在1-200个字符之间'),
    body('description')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('活码描述长度不能超过1000个字符'),
    body('base_url')
        .optional()
        .isURL()
        .withMessage('基础URL格式不正确'),
    body('is_active')
        .optional()
        .isBoolean()
        .withMessage('is_active必须是布尔值')
];

const addPollingStringsValidation = [
    body('strings')
        .isArray({ min: 1 })
        .withMessage('轮询字符串必须是非空数组'),
    body('strings.*')
        .notEmpty()
        .withMessage('轮询字符串不能为空')
        .isLength({ max: 500 })
        .withMessage('轮询字符串长度不能超过500个字符'),
    body('batch_name')
        .optional()
        .isLength({ max: 100 })
        .withMessage('批次名称长度不能超过100个字符')
];

const updatePollingStringValidation = [
    body('string_value')
        .optional()
        .notEmpty()
        .withMessage('轮询字符串不能为空')
        .isLength({ max: 500 })
        .withMessage('轮询字符串长度不能超过500个字符'),
    body('sort_order')
        .optional()
        .isInt({ min: 1 })
        .withMessage('排序顺序必须是正整数'),
    body('is_active')
        .optional()
        .isBoolean()
        .withMessage('is_active必须是布尔值'),
    body('batch_name')
        .optional()
        .isLength({ max: 100 })
        .withMessage('批次名称长度不能超过100个字符')
];

// 所有路由都需要认证
router.use(authenticateToken);
router.use(checkResourceAccess);
router.use(requireManager); // 所有用户角色都可以管理活码

// 活码管理路由
router.get('/stats', (req, res) => {
    liveCodeController.getLiveCodeStats(req, res);
});

router.get('/top', (req, res) => {
    liveCodeController.getTopLiveCodes(req, res);
});

router.get('/', (req, res) => {
    liveCodeController.getLiveCodeList(req, res);
});

router.post('/', createLiveCodeValidation, (req, res) => {
    liveCodeController.createLiveCode(req, res);
});

router.get('/:id', (req, res) => {
    liveCodeController.getLiveCodeById(req, res);
});

router.put('/:id', updateLiveCodeValidation, (req, res) => {
    liveCodeController.updateLiveCode(req, res);
});

router.delete('/:id', (req, res) => {
    liveCodeController.deleteLiveCode(req, res);
});

router.post('/:id/duplicate', (req, res) => {
    liveCodeController.duplicateLiveCode(req, res);
});

// 轮询字符串管理路由
router.post('/:id/polling-strings', addPollingStringsValidation, (req, res) => {
    liveCodeController.addPollingStrings(req, res);
});

router.put('/:id/polling-strings/:pollingId', updatePollingStringValidation, (req, res) => {
    liveCodeController.updatePollingString(req, res);
});

router.delete('/:id/polling-strings/:pollingId', (req, res) => {
    liveCodeController.deletePollingString(req, res);
});

router.post('/:id/polling-strings/reorder', (req, res) => {
    liveCodeController.reorderPollingStrings(req, res);
});

router.post('/:id/polling-strings/batch', (req, res) => {
    liveCodeController.batchOperatePollingStrings(req, res);
});

router.post('/:id/polling-strings/import', (req, res) => {
    liveCodeController.importPollingStrings(req, res);
});

router.get('/:id/polling-strings/export', (req, res) => {
    liveCodeController.exportPollingStrings(req, res);
});

// 批量操作API
router.post('/batch/create',
    body('livecodes').isArray({ min: 1, max: 10 }).withMessage('livecodes必须是包含1-10个元素的数组'),
    body('livecodes.*.title').notEmpty().withMessage('活码标题不能为空'),
    body('livecodes.*.base_url').isURL().withMessage('基础URL格式不正确'),
    (req, res) => {
        liveCodeController.batchCreateLiveCodes(req, res);
    }
);

router.post('/batch/update',
    body('updates').isArray({ min: 1, max: 50 }).withMessage('updates必须是包含1-50个元素的数组'),
    body('updates.*.id').isInt({ min: 1 }).withMessage('活码ID必须是正整数'),
    (req, res) => {
        liveCodeController.batchUpdateLiveCodes(req, res);
    }
);

router.post('/batch/delete',
    body('ids').isArray({ min: 1, max: 50 }).withMessage('ids必须是包含1-50个元素的数组'),
    body('ids.*').isInt({ min: 1 }).withMessage('活码ID必须是正整数'),
    (req, res) => {
        liveCodeController.batchDeleteLiveCodes(req, res);
    }
);

router.post('/batch/toggle-status',
    body('ids').isArray({ min: 1, max: 50 }).withMessage('ids必须是包含1-50个元素的数组'),
    body('ids.*').isInt({ min: 1 }).withMessage('活码ID必须是正整数'),
    body('is_active').isBoolean().withMessage('is_active必须是布尔值'),
    (req, res) => {
        liveCodeController.batchToggleStatus(req, res);
    }
);

// 活码分析API
router.get('/:id/analytics', (req, res) => {
    liveCodeController.getLiveCodeAnalytics(req, res);
});

router.get('/:id/analytics/timeline', (req, res) => {
    liveCodeController.getLiveCodeTimeline(req, res);
});

router.get('/:id/analytics/geo', (req, res) => {
    liveCodeController.getLiveCodeGeoStats(req, res);
});

// 活码模板API
router.get('/templates', (req, res) => {
    liveCodeController.getTemplates(req, res);
});

router.post('/templates',
    body('name').notEmpty().withMessage('模板名称不能为空'),
    body('template_data').isObject().withMessage('模板数据必须是对象'),
    (req, res) => {
        liveCodeController.createTemplate(req, res);
    }
);

router.post('/create-from-template/:templateId', (req, res) => {
    liveCodeController.createFromTemplate(req, res);
});

// 错误处理中间件
router.use((error, req, res, next) => {
    console.error('活码路由错误:', error);
    res.status(500).json({
        error: '服务器内部错误',
        message: process.env.NODE_ENV === 'development' ? error.message : '请稍后重试'
    });
});

module.exports = router;
