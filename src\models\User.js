const Database = require('better-sqlite3');
const bcrypt = require('bcryptjs');
const path = require('path');

class User {
    constructor() {
        const dbPath = process.env.DATABASE_PATH || path.join(__dirname, '../../data/livecode.db');
        this.db = new Database(dbPath);
    }

    // 创建用户
    async create(userData) {
        const { username, email, password, role = 'manager', created_by } = userData;
        
        // 检查用户名和邮箱是否已存在
        const existingUser = this.findByUsernameOrEmail(username, email);
        if (existingUser) {
            throw new Error('用户名或邮箱已存在');
        }

        // 加密密码
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
        const password_hash = await bcrypt.hash(password, saltRounds);

        const stmt = this.db.prepare(`
            INSERT INTO users (username, email, password_hash, role, created_by)
            VALUES (?, ?, ?, ?, ?)
        `);

        try {
            const result = stmt.run(username, email, password_hash, role, created_by);
            return this.findById(result.lastInsertRowid);
        } catch (error) {
            throw new Error('创建用户失败: ' + error.message);
        }
    }

    // 根据ID查找用户
    findById(id) {
        const stmt = this.db.prepare('SELECT * FROM users WHERE id = ?');
        return stmt.get(id);
    }

    // 根据用户名查找用户
    findByUsername(username) {
        const stmt = this.db.prepare('SELECT * FROM users WHERE username = ?');
        return stmt.get(username);
    }

    // 根据邮箱查找用户
    findByEmail(email) {
        const stmt = this.db.prepare('SELECT * FROM users WHERE email = ?');
        return stmt.get(email);
    }

    // 根据用户名或邮箱查找用户
    findByUsernameOrEmail(username, email) {
        const stmt = this.db.prepare('SELECT * FROM users WHERE username = ? OR email = ?');
        return stmt.get(username, email);
    }

    // 验证密码
    async validatePassword(password, hash) {
        return await bcrypt.compare(password, hash);
    }

    // 更新用户
    update(id, userData) {
        const allowedFields = ['username', 'email', 'role', 'is_active'];
        const updates = [];
        const values = [];

        for (const [key, value] of Object.entries(userData)) {
            if (allowedFields.includes(key)) {
                updates.push(`${key} = ?`);
                values.push(value);
            }
        }

        if (updates.length === 0) {
            throw new Error('没有有效的更新字段');
        }

        updates.push('updated_at = CURRENT_TIMESTAMP');
        values.push(id);

        const stmt = this.db.prepare(`
            UPDATE users SET ${updates.join(', ')} WHERE id = ?
        `);

        try {
            const result = stmt.run(...values);
            if (result.changes === 0) {
                throw new Error('用户不存在');
            }
            return this.findById(id);
        } catch (error) {
            throw new Error('更新用户失败: ' + error.message);
        }
    }

    // 更新密码
    async updatePassword(id, newPassword) {
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
        const password_hash = await bcrypt.hash(newPassword, saltRounds);

        const stmt = this.db.prepare(`
            UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?
        `);

        try {
            const result = stmt.run(password_hash, id);
            if (result.changes === 0) {
                throw new Error('用户不存在');
            }
            return true;
        } catch (error) {
            throw new Error('更新密码失败: ' + error.message);
        }
    }

    // 更新最后登录时间
    updateLastLogin(id) {
        const stmt = this.db.prepare(`
            UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
        `);
        stmt.run(id);
    }

    // 获取所有用户
    findAll(options = {}) {
        let query = 'SELECT id, username, email, role, is_active, created_at, updated_at, last_login FROM users';
        const conditions = [];
        const values = [];

        if (options.role) {
            conditions.push('role = ?');
            values.push(options.role);
        }

        if (options.is_active !== undefined) {
            conditions.push('is_active = ?');
            values.push(options.is_active);
        }

        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        query += ' ORDER BY created_at DESC';

        if (options.limit) {
            query += ' LIMIT ?';
            values.push(options.limit);
        }

        const stmt = this.db.prepare(query);
        return stmt.all(...values);
    }

    // 删除用户
    delete(id) {
        const stmt = this.db.prepare('DELETE FROM users WHERE id = ?');
        try {
            const result = stmt.run(id);
            return result.changes > 0;
        } catch (error) {
            throw new Error('删除用户失败: ' + error.message);
        }
    }

    // 获取用户统计信息
    getStats() {
        const stmt = this.db.prepare(`
            SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN role = 'super_admin' THEN 1 END) as super_admins,
                COUNT(CASE WHEN role = 'boss' THEN 1 END) as bosses,
                COUNT(CASE WHEN role = 'manager' THEN 1 END) as managers,
                COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users,
                COUNT(CASE WHEN last_login > datetime('now', '-30 days') THEN 1 END) as recent_logins
            FROM users
        `);
        return stmt.get();
    }

    // 关闭数据库连接
    close() {
        this.db.close();
    }
}

module.exports = User;
