const express = require('express');
const { body } = require('express-validator');
const AuthController = require('../controllers/authController');
const { 
    authenticateToken, 
    requireSuperAdmin, 
    requireBoss, 
    checkResourceAccess 
} = require('../middleware/auth');

const router = express.Router();
const authController = new AuthController();

// 输入验证规则
const loginValidation = [
    body('username')
        .notEmpty()
        .withMessage('用户名不能为空')
        .isLength({ min: 3, max: 50 })
        .withMessage('用户名长度必须在3-50个字符之间'),
    body('password')
        .notEmpty()
        .withMessage('密码不能为空')
        .isLength({ min: 6 })
        .withMessage('密码长度至少为6位')
];

const registerValidation = [
    body('username')
        .notEmpty()
        .withMessage('用户名不能为空')
        .isLength({ min: 3, max: 50 })
        .withMessage('用户名长度必须在3-50个字符之间')
        .matches(/^[a-zA-Z0-9_]+$/)
        .withMessage('用户名只能包含字母、数字和下划线'),
    body('email')
        .isEmail()
        .withMessage('邮箱格式不正确')
        .normalizeEmail(),
    body('password')
        .isLength({ min: 6 })
        .withMessage('密码长度至少为6位')
        .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
        .withMessage('密码必须包含至少一个字母和一个数字'),
    body('role')
        .optional()
        .isIn(['super_admin', 'boss', 'manager'])
        .withMessage('无效的用户角色')
];

const changePasswordValidation = [
    body('oldPassword')
        .notEmpty()
        .withMessage('原密码不能为空'),
    body('newPassword')
        .isLength({ min: 6 })
        .withMessage('新密码长度至少为6位')
        .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
        .withMessage('新密码必须包含至少一个字母和一个数字')
];

const resetPasswordValidation = [
    body('userId')
        .isInt({ min: 1 })
        .withMessage('用户ID必须是正整数'),
    body('newPassword')
        .isLength({ min: 6 })
        .withMessage('新密码长度至少为6位')
        .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
        .withMessage('新密码必须包含至少一个字母和一个数字')
];

const updateProfileValidation = [
    body('username')
        .optional()
        .isLength({ min: 3, max: 50 })
        .withMessage('用户名长度必须在3-50个字符之间')
        .matches(/^[a-zA-Z0-9_]+$/)
        .withMessage('用户名只能包含字母、数字和下划线'),
    body('email')
        .optional()
        .isEmail()
        .withMessage('邮箱格式不正确')
        .normalizeEmail()
];

const updateUserValidation = [
    body('username')
        .optional()
        .isLength({ min: 3, max: 50 })
        .withMessage('用户名长度必须在3-50个字符之间')
        .matches(/^[a-zA-Z0-9_]+$/)
        .withMessage('用户名只能包含字母、数字和下划线'),
    body('email')
        .optional()
        .isEmail()
        .withMessage('邮箱格式不正确')
        .normalizeEmail(),
    body('role')
        .optional()
        .isIn(['super_admin', 'boss', 'manager'])
        .withMessage('无效的用户角色'),
    body('is_active')
        .optional()
        .isBoolean()
        .withMessage('is_active必须是布尔值')
];

// 公开路由（不需要认证）
router.post('/login', loginValidation, (req, res) => {
    authController.login(req, res);
});

router.post('/refresh', (req, res) => {
    authController.refreshToken(req, res);
});

// 需要认证的路由
router.use(authenticateToken);
router.use(checkResourceAccess);

// 用户个人操作
router.get('/profile', (req, res) => {
    authController.getProfile(req, res);
});

router.put('/profile', updateProfileValidation, (req, res) => {
    authController.updateProfile(req, res);
});

router.post('/change-password', changePasswordValidation, (req, res) => {
    authController.changePassword(req, res);
});

router.post('/logout', (req, res) => {
    authController.logout(req, res);
});

router.get('/verify', (req, res) => {
    authController.verifyToken(req, res);
});

// 管理员功能（需要Boss或超级管理员权限）
router.post('/register', requireBoss, registerValidation, (req, res) => {
    authController.register(req, res);
});

router.post('/reset-password', requireBoss, resetPasswordValidation, (req, res) => {
    authController.resetPassword(req, res);
});

router.get('/users', requireBoss, (req, res) => {
    authController.getUserList(req, res);
});

router.put('/users/:id', requireBoss, updateUserValidation, (req, res) => {
    authController.updateUser(req, res);
});

router.delete('/users/:id', requireBoss, (req, res) => {
    authController.deleteUser(req, res);
});

// 错误处理中间件
router.use((error, req, res, next) => {
    console.error('认证路由错误:', error);
    res.status(500).json({
        error: '服务器内部错误',
        message: process.env.NODE_ENV === 'development' ? error.message : '请稍后重试'
    });
});

module.exports = router;
