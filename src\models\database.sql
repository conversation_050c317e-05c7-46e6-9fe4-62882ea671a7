-- 活码管理平台数据库架构
-- SQLite 数据库设计

-- 用户表 (支持三级权限: super_admin, boss, manager)
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'manager' CHECK (role IN ('super_admin', 'boss', 'manager')),
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    created_by INTEGER,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 活码表
CREATE TABLE IF NOT EXISTS live_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code VARCHAR(20) UNIQUE NOT NULL,  -- 活码标识符 (8位随机字符)
    title VARCHAR(200) NOT NULL,       -- 活码标题
    description TEXT,                  -- 活码描述
    base_url VARCHAR(500) NOT NULL,    -- 基础URL (WhatsApp URL)
    is_active BOOLEAN DEFAULT 1,       -- 是否启用
    click_count INTEGER DEFAULT 0,     -- 点击次数
    current_poll_index INTEGER DEFAULT 0, -- 当前轮询索引
    created_by INTEGER NOT NULL,       -- 创建者
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 轮询字符串表 (存储电话号码等轮询数据)
CREATE TABLE IF NOT EXISTS polling_strings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    live_code_id INTEGER NOT NULL,     -- 关联的活码ID
    string_value VARCHAR(500) NOT NULL, -- 轮询字符串值 (电话号码)
    sort_order INTEGER NOT NULL,       -- 排序顺序
    is_active BOOLEAN DEFAULT 1,       -- 是否启用
    batch_name VARCHAR(100),           -- 批次名称
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (live_code_id) REFERENCES live_codes(id) ON DELETE CASCADE
);

-- 访问记录表
CREATE TABLE IF NOT EXISTS access_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    live_code_id INTEGER NOT NULL,     -- 活码ID
    live_code VARCHAR(20) NOT NULL,    -- 活码标识符
    polling_string VARCHAR(500),       -- 使用的轮询字符串
    ip_address VARCHAR(45) NOT NULL,   -- 访问者IP地址
    user_agent TEXT,                   -- 用户代理
    country VARCHAR(100),              -- 国家
    region VARCHAR(100),               -- 地区
    city VARCHAR(100),                 -- 城市
    latitude DECIMAL(10, 8),           -- 纬度
    longitude DECIMAL(11, 8),          -- 经度
    access_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    redirect_url TEXT,                 -- 实际重定向的URL
    FOREIGN KEY (live_code_id) REFERENCES live_codes(id)
);

-- 操作审计日志表 (记录管理员操作)
CREATE TABLE IF NOT EXISTS audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,          -- 操作用户ID
    username VARCHAR(50) NOT NULL,     -- 操作用户名
    action VARCHAR(100) NOT NULL,      -- 操作类型
    resource_type VARCHAR(50),         -- 资源类型 (live_code, user, etc.)
    resource_id INTEGER,               -- 资源ID
    old_values TEXT,                   -- 操作前的值 (JSON)
    new_values TEXT,                   -- 操作后的值 (JSON)
    ip_address VARCHAR(45),            -- 操作IP
    user_agent TEXT,                   -- 用户代理
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_live_codes_code ON live_codes(code);
CREATE INDEX IF NOT EXISTS idx_live_codes_created_by ON live_codes(created_by);
CREATE INDEX IF NOT EXISTS idx_live_codes_is_active ON live_codes(is_active);

CREATE INDEX IF NOT EXISTS idx_polling_strings_live_code_id ON polling_strings(live_code_id);
CREATE INDEX IF NOT EXISTS idx_polling_strings_sort_order ON polling_strings(live_code_id, sort_order);
CREATE INDEX IF NOT EXISTS idx_polling_strings_is_active ON polling_strings(is_active);

CREATE INDEX IF NOT EXISTS idx_access_logs_live_code_id ON access_logs(live_code_id);
CREATE INDEX IF NOT EXISTS idx_access_logs_access_time ON access_logs(access_time);
CREATE INDEX IF NOT EXISTS idx_access_logs_ip_address ON access_logs(ip_address);

CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);

CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

-- 插入默认系统配置
INSERT OR IGNORE INTO system_config (config_key, config_value, description) VALUES
('site_title', '活码管理平台', '网站标题'),
('max_polling_strings', '1000', '每个活码最大轮询字符串数量'),
('default_redirect_delay', '0', '默认重定向延迟(秒)'),
('enable_geo_tracking', '1', '是否启用地理位置追踪'),
('enable_audit_log', '1', '是否启用操作审计日志');
