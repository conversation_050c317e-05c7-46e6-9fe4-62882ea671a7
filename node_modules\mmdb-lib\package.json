{"name": "mmdb-lib", "version": "2.2.1", "homepage": "https://github.com/runk/mmdb-lib", "description": "Maxmind DB (MMDB) Library", "keywords": ["maxmind", "mmdb", "geo", "geoip", "geoip2"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> @horgh", "<PERSON><PERSON> @Uman<PERSON><PERSON><PERSON>zad", "<PERSON> @oschwald"], "devDependencies": {"@types/ip-address": "7.0.0", "@types/mocha": "^10.0.1", "@types/node": "22.14.0", "@types/sinon": "17.0.4", "ip-address": "10.0.1", "mocha": "^11.0.0", "prettier": "^3.5.3", "semantic-release": "^24.0.0", "sinon": "20.0.0", "ts-node": "^10.4.0", "typescript": "5.8.2"}, "repository": {"type": "git", "url": "https://github.com/runk/mmdb-lib.git"}, "bugs": {"mail": "<EMAIL>", "url": "http://github.com/runk/mmdb-lib/issues"}, "files": ["lib"], "main": "lib/index.js", "typings": "lib/index.d.ts", "engines": {"node": ">=10", "npm": ">=6"}, "license": "MIT", "scripts": {"build": "rm -rf lib/* && tsc", "typecheck": "tsc --noEmit", "test": "mocha", "test:imports": "node test/imports/commonjs.js && node test/imports/esm.mjs && ts-node test/imports/typescript.ts", "format": "prettier --write src", "format:check": "prettier --check src", "prepublish": "npm run build", "semantic-release": "semantic-release"}}