const Database = require('better-sqlite3');
const path = require('path');

class LiveCode {
    constructor() {
        const dbPath = process.env.DATABASE_PATH || path.join(__dirname, '../../data/livecode.db');
        this.db = new Database(dbPath);
    }

    // 生成活码标识符
    generateCode() {
        const charset = process.env.LIVECODE_CHARSET || 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz23456789';
        const length = parseInt(process.env.LIVECODE_LENGTH) || 8;
        let code = '';
        
        for (let i = 0; i < length; i++) {
            code += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        
        return code;
    }

    // 生成唯一活码标识符
    generateUniqueCode() {
        let code;
        let attempts = 0;
        const maxAttempts = 100;

        do {
            code = this.generateCode();
            attempts++;
            
            if (attempts > maxAttempts) {
                throw new Error('无法生成唯一的活码标识符');
            }
        } while (this.findByCode(code));

        return code;
    }

    // 创建活码
    create(liveCodeData) {
        const { title, description, base_url, created_by } = liveCodeData;
        const code = this.generateUniqueCode();

        const stmt = this.db.prepare(`
            INSERT INTO live_codes (code, title, description, base_url, created_by)
            VALUES (?, ?, ?, ?, ?)
        `);

        try {
            const result = stmt.run(code, title, description, base_url, created_by);
            return this.findById(result.lastInsertRowid);
        } catch (error) {
            throw new Error('创建活码失败: ' + error.message);
        }
    }

    // 根据ID查找活码
    findById(id) {
        const stmt = this.db.prepare(`
            SELECT lc.*, u.username as creator_name 
            FROM live_codes lc 
            LEFT JOIN users u ON lc.created_by = u.id 
            WHERE lc.id = ?
        `);
        return stmt.get(id);
    }

    // 根据代码查找活码
    findByCode(code) {
        const stmt = this.db.prepare(`
            SELECT lc.*, u.username as creator_name 
            FROM live_codes lc 
            LEFT JOIN users u ON lc.created_by = u.id 
            WHERE lc.code = ?
        `);
        return stmt.get(code);
    }

    // 更新活码
    update(id, liveCodeData) {
        const allowedFields = ['title', 'description', 'base_url', 'is_active'];
        const updates = [];
        const values = [];

        for (const [key, value] of Object.entries(liveCodeData)) {
            if (allowedFields.includes(key)) {
                updates.push(`${key} = ?`);
                values.push(value);
            }
        }

        if (updates.length === 0) {
            throw new Error('没有有效的更新字段');
        }

        updates.push('updated_at = CURRENT_TIMESTAMP');
        values.push(id);

        const stmt = this.db.prepare(`
            UPDATE live_codes SET ${updates.join(', ')} WHERE id = ?
        `);

        try {
            const result = stmt.run(...values);
            if (result.changes === 0) {
                throw new Error('活码不存在');
            }
            return this.findById(id);
        } catch (error) {
            throw new Error('更新活码失败: ' + error.message);
        }
    }

    // 增加点击次数
    incrementClickCount(id) {
        const stmt = this.db.prepare(`
            UPDATE live_codes SET click_count = click_count + 1 WHERE id = ?
        `);
        stmt.run(id);
    }

    // 更新轮询索引
    updatePollIndex(id, newIndex) {
        const stmt = this.db.prepare(`
            UPDATE live_codes SET current_poll_index = ? WHERE id = ?
        `);
        stmt.run(newIndex, id);
    }

    // 获取下一个轮询字符串
    getNextPollingString(liveCodeId) {
        const liveCode = this.findById(liveCodeId);
        if (!liveCode) {
            throw new Error('活码不存在');
        }

        // 获取所有活跃的轮询字符串
        const pollingStrings = this.db.prepare(`
            SELECT * FROM polling_strings 
            WHERE live_code_id = ? AND is_active = 1 
            ORDER BY sort_order ASC
        `).all(liveCodeId);

        if (pollingStrings.length === 0) {
            return null;
        }

        // 获取当前索引对应的字符串
        const currentIndex = liveCode.current_poll_index % pollingStrings.length;
        const currentString = pollingStrings[currentIndex];

        // 更新到下一个索引
        const nextIndex = (currentIndex + 1) % pollingStrings.length;
        this.updatePollIndex(liveCodeId, nextIndex);

        return currentString;
    }

    // 获取所有活码
    findAll(options = {}) {
        let query = `
            SELECT lc.*, u.username as creator_name,
                   COUNT(ps.id) as polling_count
            FROM live_codes lc 
            LEFT JOIN users u ON lc.created_by = u.id 
            LEFT JOIN polling_strings ps ON lc.id = ps.live_code_id AND ps.is_active = 1
        `;
        const conditions = [];
        const values = [];

        if (options.created_by) {
            conditions.push('lc.created_by = ?');
            values.push(options.created_by);
        }

        if (options.is_active !== undefined) {
            conditions.push('lc.is_active = ?');
            values.push(options.is_active);
        }

        if (options.search) {
            conditions.push('(lc.title LIKE ? OR lc.description LIKE ? OR lc.code LIKE ?)');
            const searchTerm = `%${options.search}%`;
            values.push(searchTerm, searchTerm, searchTerm);
        }

        if (conditions.length > 0) {
            query += ' WHERE ' + conditions.join(' AND ');
        }

        query += ' GROUP BY lc.id ORDER BY lc.created_at DESC';

        if (options.limit) {
            query += ' LIMIT ?';
            values.push(options.limit);
        }

        const stmt = this.db.prepare(query);
        return stmt.all(...values);
    }

    // 删除活码
    delete(id) {
        const stmt = this.db.prepare('DELETE FROM live_codes WHERE id = ?');
        try {
            const result = stmt.run(id);
            return result.changes > 0;
        } catch (error) {
            throw new Error('删除活码失败: ' + error.message);
        }
    }

    // 获取活码统计信息
    getStats(userId = null) {
        let query = `
            SELECT 
                COUNT(*) as total_codes,
                COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_codes,
                SUM(click_count) as total_clicks,
                AVG(click_count) as avg_clicks
            FROM live_codes
        `;
        const values = [];

        if (userId) {
            query += ' WHERE created_by = ?';
            values.push(userId);
        }

        const stmt = this.db.prepare(query);
        return stmt.get(...values);
    }

    // 获取热门活码
    getTopCodes(limit = 10, userId = null) {
        let query = `
            SELECT lc.*, u.username as creator_name 
            FROM live_codes lc 
            LEFT JOIN users u ON lc.created_by = u.id 
            WHERE lc.is_active = 1
        `;
        const values = [];

        if (userId) {
            query += ' AND lc.created_by = ?';
            values.push(userId);
        }

        query += ' ORDER BY lc.click_count DESC LIMIT ?';
        values.push(limit);

        const stmt = this.db.prepare(query);
        return stmt.all(...values);
    }

    // 关闭数据库连接
    close() {
        this.db.close();
    }
}

module.exports = LiveCode;
