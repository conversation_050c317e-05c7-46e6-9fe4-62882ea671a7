import { Metadata } from './metadata';
import { Response } from './reader/response';
import { ReaderOptions } from './types';
export declare class Reader<T extends Response> {
    metadata: Metadata;
    private decoder;
    private db;
    private ipv4StartNodeNumber;
    private walker;
    private opts;
    constructor(db: <PERSON>uffer, opts?: ReaderOptions);
    load(db: Buffer): void;
    get(ipAddress: string): T | null;
    getWithPrefixLength(ipAddress: string): [T | null, number];
    private findAddressInTree;
    private resolveDataPointer;
    private ipv4Start;
}
export * from './reader/response';
