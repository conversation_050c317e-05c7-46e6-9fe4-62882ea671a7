#!/usr/bin/env node

require('dotenv').config();
const { initializeDatabase, getDatabaseStats } = require('../src/utils/database');

async function main() {
    try {
        console.log('开始初始化数据库...');
        
        await initializeDatabase();
        
        console.log('获取数据库统计信息...');
        const stats = getDatabaseStats();
        
        console.log('\n数据库初始化完成!');
        console.log('===================');
        console.log(`数据库大小: ${(stats.size / 1024).toFixed(2)} KB`);
        console.log('\n表统计:');
        for (const [table, count] of Object.entries(stats.tables)) {
            console.log(`  ${table}: ${count} 行`);
        }
        
        console.log('\n索引统计:');
        for (const index of stats.indexes) {
            console.log(`  ${index.name} (表: ${index.tbl_name})`);
        }
        
        console.log('\n默认管理员账户:');
        console.log(`  用户名: ${process.env.ADMIN_USERNAME || 'admin'}`);
        console.log(`  密码: ${process.env.ADMIN_PASSWORD || 'admin123'}`);
        console.log(`  邮箱: ${process.env.ADMIN_EMAIL || '<EMAIL>'}`);
        
        console.log('\n数据库初始化成功完成!');
        
    } catch (error) {
        console.error('数据库初始化失败:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = main;
