const express = require('express');
const { body } = require('express-validator');
const RedirectController = require('../controllers/redirectController');
const { 
    authenticateToken, 
    optionalAuth, 
    requireManager 
} = require('../middleware/auth');

const router = express.Router();
const redirectController = new RedirectController();

// 公开路由（不需要认证）
// 主要的活码重定向路由
router.get('/:code', (req, res) => {
    redirectController.handleRedirect(req, res);
});

// 活码健康检查（公开接口，用于监控）
router.get('/:code/health', (req, res) => {
    redirectController.checkHealth(req, res);
});

// 活码预览（公开接口，但不执行重定向）
router.get('/:code/preview', (req, res) => {
    redirectController.previewLiveCode(req, res);
});

// 需要认证的管理接口
router.use('/api', authenticateToken);
router.use('/api', requireManager);

// 批量健康检查
router.post('/api/health/batch', 
    body('codes')
        .isArray({ min: 1, max: 100 })
        .withMessage('codes必须是包含1-100个元素的数组'),
    body('codes.*')
        .isString()
        .withMessage('每个活码必须是字符串'),
    (req, res) => {
        redirectController.batchCheckHealth(req, res);
    }
);

// 预热活码
router.post('/api/:code/warmup', (req, res) => {
    redirectController.warmupLiveCode(req, res);
});

// 重定向统计接口
router.get('/api/stats', (req, res) => {
    redirectController.getRedirectStats(req, res);
});

router.get('/api/stats/top', (req, res) => {
    redirectController.getTopRedirects(req, res);
});

router.get('/api/stats/geo', (req, res) => {
    redirectController.getGeoStats(req, res);
});

router.get('/api/stats/time', (req, res) => {
    redirectController.getTimeStats(req, res);
});

// 错误处理中间件
router.use((error, req, res, next) => {
    console.error('重定向路由错误:', error);
    
    // 如果是API请求，返回JSON错误
    if (req.path.startsWith('/api/')) {
        return res.status(500).json({
            error: '服务器内部错误',
            message: process.env.NODE_ENV === 'development' ? error.message : '请稍后重试'
        });
    }
    
    // 否则渲染错误页面
    res.status(500).render('error', {
        title: '服务器错误',
        message: '服务暂时不可用，请稍后重试',
        code: 500
    });
});

module.exports = router;
