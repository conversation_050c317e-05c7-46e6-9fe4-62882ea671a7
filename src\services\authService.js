const User = require('../models/User');
const { generateToken, generateRefreshToken, verifyRefreshToken } = require('../middleware/auth');

class AuthService {
    constructor() {
        this.userModel = new User();
    }

    // 用户登录
    async login(username, password) {
        try {
            // 查找用户
            const user = this.userModel.findByUsername(username);
            if (!user) {
                throw new Error('用户名或密码错误');
            }

            // 检查用户是否激活
            if (!user.is_active) {
                throw new Error('账户已被禁用，请联系管理员');
            }

            // 验证密码
            const isValidPassword = await this.userModel.validatePassword(password, user.password_hash);
            if (!isValidPassword) {
                throw new Error('用户名或密码错误');
            }

            // 更新最后登录时间
            this.userModel.updateLastLogin(user.id);

            // 生成令牌
            const accessToken = generateToken(user);
            const refreshToken = generateRefreshToken(user);

            // 返回用户信息和令牌（不包含密码）
            const { password_hash, ...userInfo } = user;
            
            return {
                user: userInfo,
                accessToken,
                refreshToken,
                expiresIn: process.env.JWT_EXPIRES_IN || '24h'
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 刷新访问令牌
    async refreshToken(refreshToken) {
        try {
            // 验证刷新令牌
            const decoded = verifyRefreshToken(refreshToken);
            
            if (decoded.type !== 'refresh') {
                throw new Error('无效的刷新令牌');
            }

            // 获取用户信息
            const user = this.userModel.findById(decoded.id);
            if (!user) {
                throw new Error('用户不存在');
            }

            if (!user.is_active) {
                throw new Error('账户已被禁用');
            }

            // 生成新的访问令牌
            const accessToken = generateToken(user);
            const newRefreshToken = generateRefreshToken(user);

            return {
                accessToken,
                refreshToken: newRefreshToken,
                expiresIn: process.env.JWT_EXPIRES_IN || '24h'
            };
        } catch (error) {
            throw new Error('刷新令牌失败: ' + error.message);
        }
    }

    // 用户注册（仅限管理员创建用户）
    async register(userData, createdBy) {
        try {
            const { username, email, password, role = 'manager' } = userData;

            // 验证输入
            if (!username || !email || !password) {
                throw new Error('用户名、邮箱和密码不能为空');
            }

            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                throw new Error('邮箱格式不正确');
            }

            // 验证密码强度
            if (password.length < 6) {
                throw new Error('密码长度至少为6位');
            }

            // 验证角色
            const validRoles = ['super_admin', 'boss', 'manager'];
            if (!validRoles.includes(role)) {
                throw new Error('无效的用户角色');
            }

            // 创建用户
            const user = await this.userModel.create({
                username,
                email,
                password,
                role,
                created_by: createdBy
            });

            // 返回用户信息（不包含密码）
            const { password_hash, ...userInfo } = user;
            return userInfo;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 修改密码
    async changePassword(userId, oldPassword, newPassword) {
        try {
            // 获取用户信息
            const user = this.userModel.findById(userId);
            if (!user) {
                throw new Error('用户不存在');
            }

            // 验证旧密码
            const isValidOldPassword = await this.userModel.validatePassword(oldPassword, user.password_hash);
            if (!isValidOldPassword) {
                throw new Error('原密码错误');
            }

            // 验证新密码强度
            if (newPassword.length < 6) {
                throw new Error('新密码长度至少为6位');
            }

            if (oldPassword === newPassword) {
                throw new Error('新密码不能与原密码相同');
            }

            // 更新密码
            await this.userModel.updatePassword(userId, newPassword);

            return { message: '密码修改成功' };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 重置密码（管理员功能）
    async resetPassword(userId, newPassword, adminId) {
        try {
            // 获取目标用户
            const targetUser = this.userModel.findById(userId);
            if (!targetUser) {
                throw new Error('目标用户不存在');
            }

            // 获取管理员信息
            const admin = this.userModel.findById(adminId);
            if (!admin) {
                throw new Error('管理员用户不存在');
            }

            // 检查权限
            if (!this.canManageUser(admin.role, targetUser.role)) {
                throw new Error('权限不足，无法重置该用户密码');
            }

            // 验证新密码强度
            if (newPassword.length < 6) {
                throw new Error('新密码长度至少为6位');
            }

            // 更新密码
            await this.userModel.updatePassword(userId, newPassword);

            return { message: '密码重置成功' };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 获取用户信息
    async getUserInfo(userId) {
        try {
            const user = this.userModel.findById(userId);
            if (!user) {
                throw new Error('用户不存在');
            }

            // 返回用户信息（不包含密码）
            const { password_hash, ...userInfo } = user;
            return userInfo;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 更新用户信息
    async updateUserInfo(userId, updateData, adminId = null) {
        try {
            const allowedFields = ['username', 'email'];
            const adminFields = ['role', 'is_active'];

            // 如果是管理员操作，允许更新更多字段
            if (adminId) {
                const admin = this.userModel.findById(adminId);
                if (admin && (admin.role === 'super_admin' || admin.role === 'boss')) {
                    allowedFields.push(...adminFields);
                }
            }

            // 过滤允许的字段
            const filteredData = {};
            for (const [key, value] of Object.entries(updateData)) {
                if (allowedFields.includes(key)) {
                    filteredData[key] = value;
                }
            }

            if (Object.keys(filteredData).length === 0) {
                throw new Error('没有有效的更新字段');
            }

            // 更新用户信息
            const updatedUser = this.userModel.update(userId, filteredData);

            // 返回更新后的用户信息（不包含密码）
            const { password_hash, ...userInfo } = updatedUser;
            return userInfo;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 检查用户是否可以管理其他用户
    canManageUser(managerRole, targetRole) {
        // 超级管理员可以管理所有用户
        if (managerRole === 'super_admin') {
            return true;
        }

        // Boss可以管理Manager，但不能管理其他Boss或超级管理员
        if (managerRole === 'boss') {
            return targetRole === 'manager';
        }

        // Manager不能管理任何用户
        return false;
    }

    // 验证用户权限
    validatePermission(userRole, requiredRole) {
        const roleHierarchy = {
            'super_admin': 3,
            'boss': 2,
            'manager': 1
        };

        const userLevel = roleHierarchy[userRole] || 0;
        const requiredLevel = roleHierarchy[requiredRole] || 0;

        return userLevel >= requiredLevel;
    }

    // 获取用户列表（管理员功能）
    async getUserList(adminRole, options = {}) {
        try {
            // 根据管理员角色过滤用户
            if (adminRole === 'boss') {
                // Boss只能看到Manager
                options.role = 'manager';
            }
            // 超级管理员可以看到所有用户

            return this.userModel.findAll(options);
        } catch (error) {
            throw new Error('获取用户列表失败: ' + error.message);
        }
    }

    // 删除用户（管理员功能）
    async deleteUser(userId, adminId) {
        try {
            // 获取目标用户和管理员信息
            const targetUser = this.userModel.findById(userId);
            const admin = this.userModel.findById(adminId);

            if (!targetUser) {
                throw new Error('目标用户不存在');
            }

            if (!admin) {
                throw new Error('管理员用户不存在');
            }

            // 检查权限
            if (!this.canManageUser(admin.role, targetUser.role)) {
                throw new Error('权限不足，无法删除该用户');
            }

            // 不能删除自己
            if (userId === adminId) {
                throw new Error('不能删除自己的账户');
            }

            // 删除用户
            const success = this.userModel.delete(userId);
            if (!success) {
                throw new Error('删除用户失败');
            }

            return { message: '用户删除成功' };
        } catch (error) {
            throw new Error(error.message);
        }
    }
}

module.exports = AuthService;
