const jwt = require('jsonwebtoken');
const User = require('../models/User');

// JWT认证中间件
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
        return res.status(401).json({ 
            error: '访问被拒绝',
            message: '需要提供访问令牌' 
        });
    }

    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ 
                error: '令牌无效',
                message: '访问令牌已过期或无效' 
            });
        }

        req.user = user;
        next();
    });
}

// 权限检查中间件
function requireRole(roles) {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ 
                error: '未认证',
                message: '需要先登录' 
            });
        }

        // 如果roles是字符串，转换为数组
        const allowedRoles = Array.isArray(roles) ? roles : [roles];

        if (!allowedRoles.includes(req.user.role)) {
            return res.status(403).json({ 
                error: '权限不足',
                message: '您没有执行此操作的权限' 
            });
        }

        next();
    };
}

// 超级管理员权限
function requireSuperAdmin(req, res, next) {
    return requireRole('super_admin')(req, res, next);
}

// Boss权限（包括超级管理员）
function requireBoss(req, res, next) {
    return requireRole(['super_admin', 'boss'])(req, res, next);
}

// 管理员权限（包括所有角色）
function requireManager(req, res, next) {
    return requireRole(['super_admin', 'boss', 'manager'])(req, res, next);
}

// 检查用户是否可以访问特定资源
async function checkResourceAccess(req, res, next) {
    try {
        const userModel = new User();
        const user = userModel.findById(req.user.id);

        if (!user) {
            return res.status(404).json({ 
                error: '用户不存在',
                message: '用户账户不存在或已被删除' 
            });
        }

        if (!user.is_active) {
            return res.status(403).json({ 
                error: '账户已禁用',
                message: '您的账户已被禁用，请联系管理员' 
            });
        }

        // 更新最后登录时间
        userModel.updateLastLogin(user.id);

        // 将完整用户信息添加到请求对象
        req.userInfo = user;
        next();
    } catch (error) {
        console.error('检查资源访问权限失败:', error);
        res.status(500).json({ 
            error: '服务器错误',
            message: '检查用户权限时发生错误' 
        });
    }
}

// 可选的认证中间件（不强制要求登录）
function optionalAuth(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        req.user = null;
        return next();
    }

    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (err) {
            req.user = null;
        } else {
            req.user = user;
        }
        next();
    });
}

// 生成JWT令牌
function generateToken(user) {
    const payload = {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
    };

    const options = {
        expiresIn: process.env.JWT_EXPIRES_IN || '24h',
        issuer: process.env.JWT_ISSUER || 'livecode-platform'
    };

    return jwt.sign(payload, process.env.JWT_SECRET, options);
}

// 生成刷新令牌
function generateRefreshToken(user) {
    const payload = {
        id: user.id,
        type: 'refresh'
    };

    const options = {
        expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
        issuer: process.env.JWT_ISSUER || 'livecode-platform'
    };

    return jwt.sign(payload, process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET, options);
}

// 验证刷新令牌
function verifyRefreshToken(token) {
    try {
        return jwt.verify(token, process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET);
    } catch (error) {
        throw new Error('刷新令牌无效');
    }
}

// 权限级别检查
function hasPermission(userRole, requiredRole) {
    const roleHierarchy = {
        'super_admin': 3,
        'boss': 2,
        'manager': 1
    };

    const userLevel = roleHierarchy[userRole] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return userLevel >= requiredLevel;
}

// 检查用户是否可以管理其他用户
function canManageUser(managerRole, targetRole) {
    // 超级管理员可以管理所有用户
    if (managerRole === 'super_admin') {
        return true;
    }

    // Boss可以管理Manager，但不能管理其他Boss或超级管理员
    if (managerRole === 'boss') {
        return targetRole === 'manager';
    }

    // Manager不能管理任何用户
    return false;
}

// 资源所有权检查中间件
function checkResourceOwnership(resourceIdParam = 'id') {
    return async (req, res, next) => {
        try {
            // 超级管理员可以访问所有资源
            if (req.user.role === 'super_admin') {
                return next();
            }

            // Boss可以访问自己创建的资源
            if (req.user.role === 'boss') {
                // 这里需要根据具体资源类型来检查所有权
                // 暂时允许Boss访问，具体检查在控制器中实现
                return next();
            }

            // Manager只能访问自己创建的资源
            const resourceId = req.params[resourceIdParam];
            
            // 具体的所有权检查逻辑需要在控制器中实现
            // 因为不同资源的所有权字段可能不同
            req.checkOwnership = true;
            next();
        } catch (error) {
            console.error('检查资源所有权失败:', error);
            res.status(500).json({ 
                error: '服务器错误',
                message: '检查资源权限时发生错误' 
            });
        }
    };
}

module.exports = {
    authenticateToken,
    requireRole,
    requireSuperAdmin,
    requireBoss,
    requireManager,
    checkResourceAccess,
    optionalAuth,
    generateToken,
    generateRefreshToken,
    verifyRefreshToken,
    hasPermission,
    canManageUser,
    checkResourceOwnership
};
