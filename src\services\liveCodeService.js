const LiveCode = require('../models/LiveCode');
const PollingString = require('../models/PollingString');

class LiveCodeService {
    constructor() {
        this.liveCodeModel = new LiveCode();
        this.pollingStringModel = new PollingString();
    }

    // 创建活码
    async createLiveCode(liveCodeData, userId) {
        try {
            const { title, description, base_url, polling_strings = [] } = liveCodeData;

            // 验证输入
            if (!title || !base_url) {
                throw new Error('活码标题和基础URL不能为空');
            }

            // 验证URL格式
            try {
                new URL(base_url);
            } catch (error) {
                throw new Error('基础URL格式不正确');
            }

            // 创建活码
            const liveCode = this.liveCodeModel.create({
                title,
                description,
                base_url,
                created_by: userId
            });

            // 如果提供了轮询字符串，批量创建
            if (polling_strings.length > 0) {
                await this.addPollingStrings(liveCode.id, polling_strings);
            }

            return this.getLiveCodeById(liveCode.id);
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 获取活码详情
    async getLiveCodeById(id, userId = null) {
        try {
            const liveCode = this.liveCodeModel.findById(id);
            if (!liveCode) {
                throw new Error('活码不存在');
            }

            // 检查权限：Manager只能查看自己创建的活码
            if (userId && liveCode.created_by !== userId) {
                // 这里需要检查用户角色，如果是Manager则拒绝访问
                // 具体实现在控制器中处理
            }

            // 获取轮询字符串
            const pollingStrings = this.pollingStringModel.findByLiveCodeId(id, { is_active: 1 });
            
            return {
                ...liveCode,
                polling_strings: pollingStrings,
                polling_count: pollingStrings.length
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 获取活码列表
    async getLiveCodeList(options = {}, userId = null, userRole = null) {
        try {
            // Manager只能查看自己创建的活码
            if (userRole === 'manager') {
                options.created_by = userId;
            }

            const liveCodes = this.liveCodeModel.findAll(options);
            
            // 为每个活码添加轮询字符串统计
            const liveCodesWithStats = liveCodes.map(liveCode => {
                const pollingStats = this.pollingStringModel.getStats(liveCode.id);
                return {
                    ...liveCode,
                    ...pollingStats
                };
            });

            return liveCodesWithStats;
        } catch (error) {
            throw new Error('获取活码列表失败: ' + error.message);
        }
    }

    // 更新活码
    async updateLiveCode(id, updateData, userId, userRole) {
        try {
            const liveCode = this.liveCodeModel.findById(id);
            if (!liveCode) {
                throw new Error('活码不存在');
            }

            // 检查权限
            if (userRole === 'manager' && liveCode.created_by !== userId) {
                throw new Error('权限不足，只能修改自己创建的活码');
            }

            // 验证更新数据
            if (updateData.base_url) {
                try {
                    new URL(updateData.base_url);
                } catch (error) {
                    throw new Error('基础URL格式不正确');
                }
            }

            const updatedLiveCode = this.liveCodeModel.update(id, updateData);
            return this.getLiveCodeById(id);
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 删除活码
    async deleteLiveCode(id, userId, userRole) {
        try {
            const liveCode = this.liveCodeModel.findById(id);
            if (!liveCode) {
                throw new Error('活码不存在');
            }

            // 检查权限
            if (userRole === 'manager' && liveCode.created_by !== userId) {
                throw new Error('权限不足，只能删除自己创建的活码');
            }

            // 删除活码（会级联删除轮询字符串）
            const success = this.liveCodeModel.delete(id);
            if (!success) {
                throw new Error('删除活码失败');
            }

            return { message: '活码删除成功' };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 添加轮询字符串
    async addPollingStrings(liveCodeId, strings, batchName = null, userId = null, userRole = null) {
        try {
            // 检查活码是否存在
            const liveCode = this.liveCodeModel.findById(liveCodeId);
            if (!liveCode) {
                throw new Error('活码不存在');
            }

            // 检查权限
            if (userRole === 'manager' && liveCode.created_by !== userId) {
                throw new Error('权限不足，只能修改自己创建的活码');
            }

            // 验证输入
            if (!Array.isArray(strings) || strings.length === 0) {
                throw new Error('轮询字符串不能为空');
            }

            // 过滤空字符串
            const validStrings = strings.filter(s => s && s.trim());
            if (validStrings.length === 0) {
                throw new Error('没有有效的轮询字符串');
            }

            // 批量创建轮询字符串
            const results = this.pollingStringModel.createBatch(liveCodeId, validStrings, batchName);
            
            return {
                message: `成功添加 ${results.length} 个轮询字符串`,
                count: results.length,
                batch_name: batchName
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 更新轮询字符串
    async updatePollingString(id, updateData, userId, userRole) {
        try {
            const pollingString = this.pollingStringModel.findById(id);
            if (!pollingString) {
                throw new Error('轮询字符串不存在');
            }

            // 检查活码权限
            const liveCode = this.liveCodeModel.findById(pollingString.live_code_id);
            if (userRole === 'manager' && liveCode.created_by !== userId) {
                throw new Error('权限不足，只能修改自己创建的活码');
            }

            const updatedPollingString = this.pollingStringModel.update(id, updateData);
            return updatedPollingString;
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 删除轮询字符串
    async deletePollingString(id, userId, userRole) {
        try {
            const pollingString = this.pollingStringModel.findById(id);
            if (!pollingString) {
                throw new Error('轮询字符串不存在');
            }

            // 检查活码权限
            const liveCode = this.liveCodeModel.findById(pollingString.live_code_id);
            if (userRole === 'manager' && liveCode.created_by !== userId) {
                throw new Error('权限不足，只能修改自己创建的活码');
            }

            const success = this.pollingStringModel.delete(id);
            if (!success) {
                throw new Error('删除轮询字符串失败');
            }

            return { message: '轮询字符串删除成功' };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 重新排序轮询字符串
    async reorderPollingStrings(liveCodeId, orderedIds, userId, userRole) {
        try {
            // 检查活码权限
            const liveCode = this.liveCodeModel.findById(liveCodeId);
            if (!liveCode) {
                throw new Error('活码不存在');
            }

            if (userRole === 'manager' && liveCode.created_by !== userId) {
                throw new Error('权限不足，只能修改自己创建的活码');
            }

            const success = this.pollingStringModel.reorder(liveCodeId, orderedIds);
            if (!success) {
                throw new Error('重新排序失败');
            }

            return { message: '轮询字符串排序更新成功' };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 批量操作轮询字符串
    async batchOperatePollingStrings(liveCodeId, operation, data, userId, userRole) {
        try {
            // 检查活码权限
            const liveCode = this.liveCodeModel.findById(liveCodeId);
            if (!liveCode) {
                throw new Error('活码不存在');
            }

            if (userRole === 'manager' && liveCode.created_by !== userId) {
                throw new Error('权限不足，只能修改自己创建的活码');
            }

            let result;
            switch (operation) {
                case 'delete_batch':
                    result = this.pollingStringModel.deleteBatch(liveCodeId, data.batch_name);
                    return { message: `删除了 ${result} 个轮询字符串` };

                case 'toggle_batch':
                    result = this.pollingStringModel.toggleActiveBatch(liveCodeId, data.batch_name, data.is_active);
                    return { message: `更新了 ${result} 个轮询字符串的状态` };

                case 'clear_all':
                    result = this.pollingStringModel.clearByLiveCodeId(liveCodeId);
                    return { message: `清空了 ${result} 个轮询字符串` };

                default:
                    throw new Error('不支持的批量操作');
            }
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 导入轮询字符串
    async importPollingStrings(liveCodeId, text, options = {}, userId, userRole) {
        try {
            // 检查活码权限
            const liveCode = this.liveCodeModel.findById(liveCodeId);
            if (!liveCode) {
                throw new Error('活码不存在');
            }

            if (userRole === 'manager' && liveCode.created_by !== userId) {
                throw new Error('权限不足，只能修改自己创建的活码');
            }

            const { batch_name, separator = '\n' } = options;
            const results = this.pollingStringModel.importFromText(liveCodeId, text, batch_name, separator);

            return {
                message: `成功导入 ${results.length} 个轮询字符串`,
                count: results.length,
                batch_name
            };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 导出轮询字符串
    async exportPollingStrings(liveCodeId, options = {}, userId, userRole) {
        try {
            // 检查活码权限
            const liveCode = this.liveCodeModel.findById(liveCodeId);
            if (!liveCode) {
                throw new Error('活码不存在');
            }

            if (userRole === 'manager' && liveCode.created_by !== userId) {
                throw new Error('权限不足，只能查看自己创建的活码');
            }

            const text = this.pollingStringModel.exportToText(liveCodeId, options);
            return { text };
        } catch (error) {
            throw new Error(error.message);
        }
    }

    // 获取活码统计信息
    async getLiveCodeStats(userId = null, userRole = null) {
        try {
            let stats;
            
            if (userRole === 'manager') {
                // Manager只能看自己的统计
                stats = this.liveCodeModel.getStats(userId);
            } else {
                // Boss和超级管理员可以看全局统计
                stats = this.liveCodeModel.getStats();
            }

            return stats;
        } catch (error) {
            throw new Error('获取统计信息失败: ' + error.message);
        }
    }

    // 获取热门活码
    async getTopLiveCodes(limit = 10, userId = null, userRole = null) {
        try {
            let topCodes;
            
            if (userRole === 'manager') {
                // Manager只能看自己的热门活码
                topCodes = this.liveCodeModel.getTopCodes(limit, userId);
            } else {
                // Boss和超级管理员可以看全局热门活码
                topCodes = this.liveCodeModel.getTopCodes(limit);
            }

            return topCodes;
        } catch (error) {
            throw new Error('获取热门活码失败: ' + error.message);
        }
    }

    // 复制活码
    async duplicateLiveCode(id, userId, userRole) {
        try {
            const originalLiveCode = this.liveCodeModel.findById(id);
            if (!originalLiveCode) {
                throw new Error('原活码不存在');
            }

            // 检查权限
            if (userRole === 'manager' && originalLiveCode.created_by !== userId) {
                throw new Error('权限不足，只能复制自己创建的活码');
            }

            // 创建新活码
            const newLiveCode = this.liveCodeModel.create({
                title: `${originalLiveCode.title} (副本)`,
                description: originalLiveCode.description,
                base_url: originalLiveCode.base_url,
                created_by: userId
            });

            // 复制轮询字符串
            const originalPollingStrings = this.pollingStringModel.findByLiveCodeId(id);
            if (originalPollingStrings.length > 0) {
                const strings = originalPollingStrings.map(ps => ps.string_value);
                this.pollingStringModel.createBatch(newLiveCode.id, strings, '复制的轮询字符串');
            }

            return this.getLiveCodeById(newLiveCode.id);
        } catch (error) {
            throw new Error(error.message);
        }
    }
}

module.exports = LiveCodeService;
