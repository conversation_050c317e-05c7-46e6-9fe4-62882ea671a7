import { Reader, Response } from 'mmdb-lib';
type Callback = () => void;
export interface OpenOpts {
    cache?: {
        max: number;
    };
    watchForUpdates?: boolean;
    watchForUpdatesNonPersistent?: boolean;
    watchForUpdatesHook?: Callback;
}
export declare const open: <T extends Response>(filepath: string, opts?: OpenOpts, cb?: Callback) => Promise<Reader<T>>;
export declare const openSync: () => never;
export declare const init: () => never;
export declare const validate: (ip: string) => boolean;
export * from 'mmdb-lib';
declare const _default: {
    init: () => never;
    open: <T extends Response>(filepath: string, opts?: OpenOpts, cb?: Callback) => Promise<Reader<T>>;
    openSync: () => never;
    validate: (ip: string) => boolean;
};
export default _default;
export { Reader };
