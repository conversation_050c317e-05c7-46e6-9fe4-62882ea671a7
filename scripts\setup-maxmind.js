#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');

class MaxMindSetup {
    constructor() {
        this.dataDir = path.join(process.cwd(), 'data', 'maxmind');
        this.requiredFiles = [
            'GeoLite2-City.mmdb',
            'GeoLite2-Country.mmdb',
            'GeoLite2-ASN.mmdb'
        ];
    }

    // 创建数据目录
    createDataDirectory() {
        console.log('创建MaxMind数据目录...');
        
        if (!fs.existsSync(this.dataDir)) {
            fs.mkdirSync(this.dataDir, { recursive: true });
            console.log('✓ 数据目录创建成功:', this.dataDir);
        } else {
            console.log('✓ 数据目录已存在:', this.dataDir);
        }
    }

    // 检查现有文件
    checkExistingFiles() {
        console.log('\n检查现有MaxMind数据库文件...');
        
        const existingFiles = [];
        const missingFiles = [];

        for (const file of this.requiredFiles) {
            const filePath = path.join(this.dataDir, file);
            if (fs.existsSync(filePath)) {
                const stats = fs.statSync(filePath);
                existingFiles.push({
                    name: file,
                    size: this.formatFileSize(stats.size),
                    modified: stats.mtime.toISOString().split('T')[0]
                });
            } else {
                missingFiles.push(file);
            }
        }

        if (existingFiles.length > 0) {
            console.log('\n已存在的文件:');
            existingFiles.forEach(file => {
                console.log(`  ✓ ${file.name} (${file.size}, 修改日期: ${file.modified})`);
            });
        }

        if (missingFiles.length > 0) {
            console.log('\n缺少的文件:');
            missingFiles.forEach(file => {
                console.log(`  ✗ ${file}`);
            });
        }

        return { existingFiles, missingFiles };
    }

    // 格式化文件大小
    formatFileSize(bytes) {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    // 显示下载说明
    showDownloadInstructions() {
        console.log('\n' + '='.repeat(80));
        console.log('MaxMind GeoLite2 数据库下载说明');
        console.log('='.repeat(80));
        
        console.log('\n由于许可证限制，MaxMind数据库需要手动下载。请按照以下步骤操作：');
        
        console.log('\n1. 访问 MaxMind 官网注册免费账户:');
        console.log('   https://dev.maxmind.com/geoip/geolite2-free-geolocation-data');
        
        console.log('\n2. 登录后，下载以下数据库文件:');
        this.requiredFiles.forEach(file => {
            console.log(`   • ${file}`);
        });
        
        console.log('\n3. 将下载的 .mmdb 文件放置到以下目录:');
        console.log(`   ${this.dataDir}`);
        
        console.log('\n4. 确保文件名完全匹配（区分大小写）');
        
        console.log('\n5. 重新运行此脚本验证安装:');
        console.log('   npm run setup:maxmind');
        
        console.log('\n注意事项:');
        console.log('• GeoLite2-City.mmdb 和 GeoLite2-Country.mmdb 是必需的');
        console.log('• GeoLite2-ASN.mmdb 是可选的，但建议安装');
        console.log('• 数据库文件通常每月更新一次');
        console.log('• 文件大小约为: City(~70MB), Country(~6MB), ASN(~4MB)');
    }

    // 验证数据库文件
    validateDatabases() {
        console.log('\n验证MaxMind数据库文件...');
        
        const results = [];
        
        for (const file of this.requiredFiles) {
            const filePath = path.join(this.dataDir, file);
            const result = {
                name: file,
                path: filePath,
                exists: false,
                valid: false,
                size: 0,
                error: null
            };

            if (fs.existsSync(filePath)) {
                result.exists = true;
                const stats = fs.statSync(filePath);
                result.size = stats.size;

                // 基本验证：检查文件大小和扩展名
                if (stats.size > 1000 && file.endsWith('.mmdb')) {
                    result.valid = true;
                } else {
                    result.error = '文件大小异常或格式不正确';
                }
            } else {
                result.error = '文件不存在';
            }

            results.push(result);
        }

        // 显示验证结果
        console.log('\n验证结果:');
        results.forEach(result => {
            const status = result.valid ? '✓' : '✗';
            const size = result.exists ? ` (${this.formatFileSize(result.size)})` : '';
            const error = result.error ? ` - ${result.error}` : '';
            console.log(`  ${status} ${result.name}${size}${error}`);
        });

        const validFiles = results.filter(r => r.valid);
        const requiredValid = results.filter(r => r.valid && ['GeoLite2-City.mmdb', 'GeoLite2-Country.mmdb'].includes(r.name));

        console.log(`\n总结: ${validFiles.length}/${this.requiredFiles.length} 个文件有效`);
        
        if (requiredValid.length >= 2) {
            console.log('✓ 必需的数据库文件已就绪，地理位置服务可以正常工作');
            return true;
        } else {
            console.log('✗ 缺少必需的数据库文件，地理位置服务将无法工作');
            return false;
        }
    }

    // 创建环境变量配置说明
    showEnvironmentConfig() {
        console.log('\n' + '='.repeat(80));
        console.log('环境变量配置');
        console.log('='.repeat(80));
        
        console.log('\n在 .env 文件中添加以下配置来启用地理位置追踪:');
        console.log('');
        console.log('# 启用地理位置追踪 (1=启用, 0=禁用)');
        console.log('ENABLE_GEO_TRACKING=1');
        console.log('');
        console.log('# MaxMind数据库路径 (可选，默认为 data/maxmind)');
        console.log('# MAXMIND_DATA_DIR=data/maxmind');
    }

    // 测试MaxMind服务
    async testMaxMindService() {
        console.log('\n测试MaxMind地理位置服务...');
        
        try {
            // 动态导入地理位置服务
            const geoLocationService = require('../src/services/geoLocationService');
            
            const testResult = await geoLocationService.testService();
            
            console.log('\n测试结果:');
            console.log(`状态: ${testResult.status}`);
            
            if (testResult.service_status) {
                const status = testResult.service_status;
                console.log(`初始化状态: ${status.initialized ? '✓' : '✗'}`);
                console.log(`City数据库: ${status.city_db_available ? '✓' : '✗'}`);
                console.log(`Country数据库: ${status.country_db_available ? '✓' : '✗'}`);
                console.log(`ASN数据库: ${status.asn_db_available ? '✓' : '✗'}`);
                console.log(`地理追踪启用: ${status.enabled ? '✓' : '✗'}`);
            }

            if (testResult.test_results) {
                console.log('\nIP测试结果:');
                Object.entries(testResult.test_results).forEach(([ip, result]) => {
                    if (result) {
                        console.log(`  ${ip}: ${result.country || 'Unknown'} ${result.city || ''}`);
                    } else {
                        console.log(`  ${ip}: 查询失败`);
                    }
                });
            }

            return testResult.status === 'success';
            
        } catch (error) {
            console.error('测试失败:', error.message);
            return false;
        }
    }

    // 主要设置流程
    async run() {
        console.log('MaxMind GeoLite2 数据库设置工具');
        console.log('='.repeat(50));

        // 1. 创建数据目录
        this.createDataDirectory();

        // 2. 检查现有文件
        const { existingFiles, missingFiles } = this.checkExistingFiles();

        // 3. 如果有缺少的文件，显示下载说明
        if (missingFiles.length > 0) {
            this.showDownloadInstructions();
        }

        // 4. 验证数据库文件
        const isValid = this.validateDatabases();

        // 5. 显示环境变量配置
        this.showEnvironmentConfig();

        // 6. 如果文件有效，测试服务
        if (isValid) {
            const testSuccess = await this.testMaxMindService();
            
            if (testSuccess) {
                console.log('\n🎉 MaxMind地理位置服务设置完成并测试通过！');
            } else {
                console.log('\n⚠️  MaxMind数据库文件存在，但服务测试失败。请检查配置。');
            }
        } else {
            console.log('\n⚠️  请按照上述说明下载必需的数据库文件。');
        }

        console.log('\n设置完成。');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const setup = new MaxMindSetup();
    setup.run().catch(error => {
        console.error('设置过程中发生错误:', error);
        process.exit(1);
    });
}

module.exports = MaxMindSetup;
