const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');
const bcrypt = require('bcryptjs');

// 数据库连接单例
let dbInstance = null;

function getDatabase() {
    if (!dbInstance) {
        const dbPath = process.env.DATABASE_PATH || path.join(__dirname, '../../data/livecode.db');
        
        // 确保数据目录存在
        const dataDir = path.dirname(dbPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        dbInstance = new Database(dbPath);
        
        // 启用外键约束
        dbInstance.pragma('foreign_keys = ON');
        
        // 设置WAL模式以提高并发性能
        dbInstance.pragma('journal_mode = WAL');
        
        // 设置同步模式
        dbInstance.pragma('synchronous = NORMAL');
        
        // 设置缓存大小
        dbInstance.pragma('cache_size = 10000');
    }
    
    return dbInstance;
}

// 初始化数据库
async function initializeDatabase() {
    const db = getDatabase();
    
    try {
        // 读取并执行SQL架构文件
        const schemaPath = path.join(__dirname, '../models/database.sql');
        const schema = fs.readFileSync(schemaPath, 'utf8');
        
        // 分割SQL语句并执行
        const statements = schema.split(';').filter(stmt => stmt.trim());
        
        for (const statement of statements) {
            if (statement.trim()) {
                db.exec(statement);
            }
        }
        
        console.log('数据库架构初始化完成');
        
        // 创建默认管理员用户
        await createDefaultAdmin();
        
        console.log('数据库初始化完成');
        
    } catch (error) {
        console.error('数据库初始化失败:', error);
        throw error;
    }
}

// 创建默认管理员用户
async function createDefaultAdmin() {
    const db = getDatabase();
    
    // 检查是否已存在管理员用户
    const existingAdmin = db.prepare('SELECT id FROM users WHERE role = ?').get('super_admin');
    
    if (!existingAdmin) {
        const username = process.env.ADMIN_USERNAME || 'admin';
        const password = process.env.ADMIN_PASSWORD || 'admin123';
        const email = process.env.ADMIN_EMAIL || '<EMAIL>';
        
        // 加密密码
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
        const password_hash = await bcrypt.hash(password, saltRounds);
        
        const stmt = db.prepare(`
            INSERT INTO users (username, email, password_hash, role)
            VALUES (?, ?, ?, ?)
        `);
        
        try {
            stmt.run(username, email, password_hash, 'super_admin');
            console.log(`默认管理员用户创建成功: ${username}`);
        } catch (error) {
            console.error('创建默认管理员失败:', error);
        }
    }
}

// 数据库备份
function backupDatabase() {
    const db = getDatabase();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(__dirname, '../../data/backups', `backup-${timestamp}.db`);
    
    // 确保备份目录存在
    const backupDir = path.dirname(backupPath);
    if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
    }
    
    try {
        db.backup(backupPath);
        console.log(`数据库备份完成: ${backupPath}`);
        return backupPath;
    } catch (error) {
        console.error('数据库备份失败:', error);
        throw error;
    }
}

// 数据库恢复
function restoreDatabase(backupPath) {
    if (!fs.existsSync(backupPath)) {
        throw new Error('备份文件不存在');
    }
    
    const currentDbPath = process.env.DATABASE_PATH || path.join(__dirname, '../../data/livecode.db');
    
    try {
        // 关闭当前数据库连接
        if (dbInstance) {
            dbInstance.close();
            dbInstance = null;
        }
        
        // 复制备份文件
        fs.copyFileSync(backupPath, currentDbPath);
        
        // 重新初始化数据库连接
        getDatabase();
        
        console.log(`数据库恢复完成: ${backupPath}`);
    } catch (error) {
        console.error('数据库恢复失败:', error);
        throw error;
    }
}

// 数据库优化
function optimizeDatabase() {
    const db = getDatabase();
    
    try {
        // 分析查询计划
        db.pragma('analyze');
        
        // 清理未使用的页面
        db.pragma('vacuum');
        
        // 重建索引
        db.exec('REINDEX');
        
        console.log('数据库优化完成');
    } catch (error) {
        console.error('数据库优化失败:', error);
        throw error;
    }
}

// 获取数据库统计信息
function getDatabaseStats() {
    const db = getDatabase();
    
    try {
        const stats = {
            // 数据库大小
            size: db.pragma('page_size') * db.pragma('page_count'),
            
            // 表统计
            tables: {},
            
            // 索引统计
            indexes: db.prepare(`
                SELECT name, tbl_name 
                FROM sqlite_master 
                WHERE type = 'index' AND name NOT LIKE 'sqlite_%'
            `).all()
        };
        
        // 获取每个表的行数
        const tables = db.prepare(`
            SELECT name FROM sqlite_master 
            WHERE type = 'table' AND name NOT LIKE 'sqlite_%'
        `).all();
        
        for (const table of tables) {
            const count = db.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
            stats.tables[table.name] = count.count;
        }
        
        return stats;
    } catch (error) {
        console.error('获取数据库统计失败:', error);
        throw error;
    }
}

// 执行数据库迁移
function runMigrations() {
    const db = getDatabase();
    const migrationsDir = path.join(__dirname, '../migrations');
    
    // 创建迁移表
    db.exec(`
        CREATE TABLE IF NOT EXISTS migrations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename VARCHAR(255) UNIQUE NOT NULL,
            executed_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    `);
    
    if (!fs.existsSync(migrationsDir)) {
        console.log('没有找到迁移文件目录');
        return;
    }
    
    // 获取已执行的迁移
    const executedMigrations = db.prepare('SELECT filename FROM migrations').all()
        .map(row => row.filename);
    
    // 获取所有迁移文件
    const migrationFiles = fs.readdirSync(migrationsDir)
        .filter(file => file.endsWith('.sql'))
        .sort();
    
    for (const filename of migrationFiles) {
        if (!executedMigrations.includes(filename)) {
            console.log(`执行迁移: ${filename}`);
            
            try {
                const migrationPath = path.join(migrationsDir, filename);
                const migration = fs.readFileSync(migrationPath, 'utf8');
                
                // 在事务中执行迁移
                const transaction = db.transaction(() => {
                    db.exec(migration);
                    db.prepare('INSERT INTO migrations (filename) VALUES (?)').run(filename);
                });
                
                transaction();
                console.log(`迁移完成: ${filename}`);
            } catch (error) {
                console.error(`迁移失败 ${filename}:`, error);
                throw error;
            }
        }
    }
}

// 清理数据库
function cleanupDatabase() {
    const db = getDatabase();
    
    try {
        // 删除90天前的访问日志
        const deleteOldLogs = db.prepare(`
            DELETE FROM access_logs 
            WHERE access_time < datetime('now', '-90 days')
        `);
        const deletedLogs = deleteOldLogs.run();
        
        // 删除30天前的审计日志
        const deleteOldAudits = db.prepare(`
            DELETE FROM audit_logs 
            WHERE created_at < datetime('now', '-30 days')
        `);
        const deletedAudits = deleteOldAudits.run();
        
        console.log(`清理完成: 删除了 ${deletedLogs.changes} 条访问日志, ${deletedAudits.changes} 条审计日志`);
        
        return {
            deletedLogs: deletedLogs.changes,
            deletedAudits: deletedAudits.changes
        };
    } catch (error) {
        console.error('数据库清理失败:', error);
        throw error;
    }
}

// 关闭数据库连接
function closeDatabase() {
    if (dbInstance) {
        dbInstance.close();
        dbInstance = null;
        console.log('数据库连接已关闭');
    }
}

// 导出函数
module.exports = {
    getDatabase,
    initializeDatabase,
    createDefaultAdmin,
    backupDatabase,
    restoreDatabase,
    optimizeDatabase,
    getDatabaseStats,
    runMigrations,
    cleanupDatabase,
    closeDatabase
};
